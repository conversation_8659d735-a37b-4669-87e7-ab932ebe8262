<div class="modal" id="ModalLoginForm">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title text-primary"><?php echo app('translator')->get('modules.applicationSetting.formSettings'); ?></h4>
        <button class="close" data-dismiss="modal" type="button">&times;</button>
      </div>
      <div class="modal-body">
        <form class="ajax-form" id="editSettings">
          <?php echo csrf_field(); ?>
          <?php echo method_field('PUT'); ?>

          <div class="form-group">
            <label for="address"><?php echo app('translator')->get('modules.applicationSetting.legalTermText'); ?></label>
            <div>
              <textarea class="form-control" id="legal_term" name="legal_term" placeholder="Enter text ..." rows="15"></textarea>

            </div>
          </div>
          <div class="form-group">
            <h4 class="card-title text-primary mb-4"><?php echo app('translator')->get('modules.applicationSetting.mailSettings'); ?></h4>
            <div>
              <label style="margin-left: 0px">Send mail if candidate move to </label>
              <div style="margin-left: -38px;">
                <ul id="assetNameMenu">
                </ul>
              </div>
            </div>
          </div>

          <div class="form-group">
            <div>
              <button class="btn btn-success waves-effect waves-light m-r-10" id="save-form" type="button">
                <?php echo app('translator')->get('app.save'); ?>
              </button>
              <button class="btn btn-inverse waves-effect waves-light" type="button"><?php echo app('translator')->get('app.reset'); ?></button>
            </div>
          </div>
        </form>

      </div>
    </div><!-- /.modal-content -->
  </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<?php $__env->startPush('footer-script'); ?>
  <script src="<?php echo e(asset('assets/node_modules/bootstrap-select/bootstrap-select.min.js')); ?>" type="text/javascript"></script>
  <script src="<?php echo e(asset('assets/node_modules/html5-editor/wysihtml5-0.3.0.js')); ?>" type="text/javascript"></script>
  <script src="<?php echo e(asset('assets/node_modules/html5-editor/bootstrap-wysihtml5.js')); ?>" type="text/javascript"></script>
  <script src="<?php echo e(asset('assets/plugins/iCheck/icheck.min.js')); ?>"></script>

  <script>
    $('#save-form').click(function() {
      $.easyAjax({
        url: '<?php echo e(route('admin.application-setting.update', $global->id)); ?>',
        container: '#editSettings',
        type: "POST",
        redirect: true,
        file: true
      })
      // $('#ModalLoginForm').modal('toggle'); 
      return false;
    });
  </script>
<?php $__env->stopPush(); ?>
<?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/application-setting/modal.blade.php ENDPATH**/ ?>