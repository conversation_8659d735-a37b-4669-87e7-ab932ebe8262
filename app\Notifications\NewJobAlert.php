<?php

namespace App\Notifications;

use App\Job;
use App\Traits\SmtpSettings;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class NewJobAlert extends Notification
{
    use Queueable, SmtpSettings;

    protected $job;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(Job $job)
    {
        $this->job = $job;
        $this->setMailConfigs();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $location = $this->job->location->location ?? 'No especificada';
        $contractType = $this->job->job_type->job_type ?? 'No especificado';
        $jobLink = route('jobs.jobDetail', $this->job->slug);
        
        return (new MailMessage)
            ->subject(__('email.newJobAlert.subject'))
            ->greeting(__('email.hello').' ' . ucwords($notifiable->email) . '!')
            ->line(str_replace(['{candidate_name}', '{job_title}', '{location}', '{contract_type}', '{job_link}'], 
                [$notifiable->email, $this->job->title, $location, $contractType, $jobLink], 
                __('email.newJobAlert.body')))
            ->action(__('email.newJobAlert.applyNow'), $jobLink)
            ->line(__('email.thankyouNote'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'data' => $this->job->toArray()
        ];
    }
}
