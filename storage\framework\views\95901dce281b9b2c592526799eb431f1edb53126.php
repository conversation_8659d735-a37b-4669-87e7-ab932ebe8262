
<style>
  .header.inner-header {
    height: 274px;
  }

  .header {
    padding: 104px 100px !important;
  }

  @media (max-width:767px) {
    .header.inner-header {
      height: 224px;
    }
  }
</style>
<?php $__env->startSection('header-text'); ?>
  <h1 class="hidden-sm-down fs-40 text-white"><?php echo e(ucwords($job->jobDescription?->name)); ?></h1>
  <h2 class="hidden-sm-down text-white"><?php echo e(ucwords($job->title)); ?></h2>
  <h3 class="hidden-sm-up mb-10 text-white"><?php echo e(ucwords($job->jobDescription?->name)); ?></h3>
  <div class="text-white">
    <a
      class="text-white"
      href="<?php echo e(route('jobs.jobOpenings')); ?>"
    ><u><?php echo app('translator')->get('modules.front.jobOpenings'); ?></u>&nbsp; </a> &raquo; &nbsp;<span class="text-white"><?php echo e(ucwords($job->jobDescription?->name ?? $job->title)); ?></span>
  </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
  <section class="section pb-40">
    <div class="container">
      <div class="row">

        <div class="col-lg-9 col-md-7">

          <div class="p-15 bg-white">
            <div class="col-md-12">
              <h4 class="mt-10"><?php echo e(ucwords($job->jobDescription?->name)); ?></h4>
              <!-- <?php if($job->company->show_in_frontend == 'true'): ?>
  <small class="company-title"><?php echo app('translator')->get('app.by'); ?> <?php echo e(ucwords($job->company->company_name)); ?></small>
  <?php endif; ?>
                              <p><?php echo e(ucwords($job->category->name)); ?></p> -->

              <?php if(count($job->skills) > 0): ?>
                <h6><?php echo app('translator')->get('menu.skills'); ?></h6>
                <div class="gap-multiline-items-1">
                  <?php $__currentLoopData = $job->skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <span class="badge badge-secondary"><?php echo e($skill->skill->name); ?></span>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
              <?php endif; ?>

              <p class="mt-30 fw-500 fs-16 text-dark"><?php echo app('translator')->get('modules.jobs.jobDescription'); ?></p>

              <div class="fw-400 text-color fs-16 text-justify">
                <?php echo $job->job_description; ?>

              </div>

              <p class="mt-30 fw-500 fs-16 text-dark"><?php echo app('translator')->get('modules.jobs.jobRequirement'); ?></p>

              <div class="fw-400 text-color fs-16 text-justify">
                <?php echo $job->job_requirement; ?>

              </div>

            </div>

          </div>

        </div>

        <div class="col-lg-3 col-md-5">
          <div
            class="sidebar bg-white"
            id="sidebar"
          >

            <div class="p-30 bb-1">
              <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('menu.postedby'); ?></p>
              <p class="fw-400 text-color fs-16 company-title mb-0">
                <?php if($job->company->show_in_frontend == 'true'): ?>
                  <?php echo app('translator')->get('app.by'); ?> <?php echo e(ucwords($job->company->company_name)); ?>

                <?php endif; ?>
              </p>
            </div>

            <div class="p-30 bb-1">
              <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('menu.locations'); ?></p>
              <p class="fw-400 text-color fs-16 mb-0"><?php echo e($locations ? ucwords($locations->location) : null); ?></p>
            </div>

            <div class="p-30 bb-1">
              <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('menu.jobCategory'); ?></p>
              <p class="fw-400 text-color fs-16 mb-0"><?php echo e(ucwords($job->category->name)); ?></p>
            </div>
            <?php if(count($job->skills) > 0): ?>
              <div class="p-30 bb-1">
                <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('menu.skills'); ?></p>
                <?php $__currentLoopData = $job->skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <p class="fw-400 text-color fs-16 mb-0"><?php echo e($skill->skill->name); ?></p>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </div>
            <?php endif; ?>

            <?php if($job->show_work_experience): ?>
              <div class="p-30 bb-1">
                <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('modules.jobs.workExperience'); ?></p>
                <p class="fw-400 text-color fs-16 mb-0">
                  <?php echo e($job->workExperience ? $job->workExperience->work_experience : '--'); ?></p>
              </div>
            <?php endif; ?>

            <div class="p-30 bb-1 visible-print">

              <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('modules.jobs.scantopay'); ?></p>
              <?php echo QrCode::size(200)->generate(route('jobs.jobApply', [$job->slug, $locations ? $locations->id : null])); ?>

            </div>

            <?php if($job->show_job_type): ?>
              <div class="p-30 bb-1">
                <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('modules.jobs.jobType'); ?></p>
                <p class="fw-400 text-color fs-16 mb-0"><?php echo e($job->jobType ? $job->jobType->job_type : '--'); ?></p>
              </div>
            <?php endif; ?>

            <?php if($job->show_salary): ?>
              <?php if($job->pay_type == 'Range'): ?>
                <div class="p-30 bb-1">
                  <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('menu.salary'); ?> <?php echo app('translator')->get('modules.jobs.range'); ?></p>
                  <p class="fw-400 text-color fs-16 mb-0">
                    <?php echo e($job->starting_salary . '--' . $job->maximum_salary . ' ' . '/' . $job->pay_according); ?> </p>
                </div>
              <?php elseif($job->pay_type == 'Starting'): ?>
                <div class="p-30 bb-1">
                  <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('modules.jobs.startingSalary'); ?></p>
                  <p class="fw-400 text-color fs-16 mb-0"><?php echo e($job->starting_salary . ' ' . '/' . $job->pay_according); ?>

                  </p>
                </div>
              <?php elseif($job->pay_type == 'Maximum'): ?>
                <div class="p-30 bb-1">
                  <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('modules.jobs.maximumSalary'); ?></p>
                  <p class="fw-400 text-color fs-16 mb-0"><?php echo e($job->starting_salary . ' ' . '/' . $job->pay_according); ?>

                  </p>
                </div>
              <?php elseif($job->pay_type == 'Exact Amount'): ?>
                <div class="p-30 bb-1">
                  <p class="fw-500 fs-16 text-dark mb-0"><?php echo app('translator')->get('modules.jobs.exactSalary'); ?></p>
                  <p class="fw-400 text-color fs-16 mb-0"><?php echo e($job->starting_salary . ' ' . '/' . $job->pay_according); ?>

                  </p>
                </div>
              <?php endif; ?>
            <?php endif; ?>
            <div class="p-30">
              <a
                class="btn btn-block btn-primary theme-background"
                href="<?php echo e(route('jobs.jobApply', [$job->slug, $locations ? $locations->id : null])); ?>"
              ><?php echo app('translator')->get('modules.front.applyForJob'); ?></a>
            </div>



            <div class="border-light mt-20 py-10 text-center">
              <span class="fs-12 fw-600"><?php echo app('translator')->get('modules.front.shareJob'); ?></span>

              <div class="social social-boxed social-colored social-cycling my-10 text-center">
                <a
                  class="social-linkedin"
                  href="https://www.linkedin.com/shareArticle?mini=true&url=<?php echo e(route('jobs.jobDetail', [$job->slug])); ?>&title=<?php echo e(urlencode(ucwords($job->title))); ?>&source=LinkedIn"
                ><i class="fa fa-linkedin"></i></a>
                <a
                  class="social-facebook"
                  href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(route('jobs.jobDetail', [$job->slug])); ?>"
                ><i class="fa fa-facebook"></i></a>
                <a
                  class="social-twitter"
                  href="https://twitter.com/intent/tweet?status=<?php echo e(route('jobs.jobDetail', [$job->slug])); ?>"
                ><i class="fa fa-twitter"></i></a>
                <a
                  class="social-gplus"
                  href="https://plus.google.com/share?url=<?php echo e(route('jobs.jobDetail', [$job->slug])); ?>"
                ><i class="fa fa-google-plus"></i></a>
                <a
                  class="social-gplus text-green"
                  href="https://wa.me/?text=<?php echo e(route('jobs.jobDetail', [$job->slug])); ?>"
                  style="background-color:#7CD567"
                ><i class="fa fa-whatsapp"></i></a>
              </div>
            </div>
            <?php if($linkedinGlobal->status == 'enable'): ?>
              <a
                class="applyWithLinkedin btn btn-block btn-primary my-10"
                href="<?php echo e(route('jobs.linkedinRedirect', 'linkedin')); ?>"
              >
                <i class="fa fa-linkedin-square"></i>
                <?php echo app('translator')->get('modules.front.linkedinSignin'); ?>
              </a>
            <?php endif; ?>
          </div>
        </div>

      </div>
    </div>
  </section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.front', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/front/job-detail.blade.php ENDPATH**/ ?>