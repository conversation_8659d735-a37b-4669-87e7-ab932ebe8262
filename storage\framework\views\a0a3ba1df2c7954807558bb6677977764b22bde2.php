

<?php if(in_array('add_job_applications', $userPermissions)): ?>
  <?php $__env->startSection('create-button'); ?>
    <a
      class="btn btn-dark btn-sm m-l-15"
      href="<?php echo e(route('admin.job-applications.create')); ?>"
    ><i class="fa fa-plus-circle"></i> <?php echo app('translator')->get('app.createNew'); ?></a>
  <?php $__env->stopSection(); ?>
<?php endif; ?>

<?php $__env->startPush('head-script'); ?>
  <link
    href="<?php echo e(asset('assets/plugins/jQueryUI/jquery-ui.min.css')); ?>"
    rel="stylesheet"
  >
  <link
    href="<?php echo e(asset('assets/lobipanel/dist/css/lobipanel.min.css')); ?>"
    rel="stylesheet"
  >
  <link
    href="<?php echo e(asset('assets/node_modules/bootstrap-datepicker/bootstrap-datepicker.min.css')); ?>"
    rel="stylesheet"
  >
  <link
    href="<?php echo e(asset('assets/node_modules/multiselect/css/multi-select.css')); ?>"
    rel="stylesheet"
  >
  <link
    href="<?php echo e(asset('assets/plugins/jquery-bar-rating-master/dist/themes/fontawesome-stars.css')); ?>"
    rel="stylesheet"
  >
  <link
    href="<?php echo e(asset('assets/node_modules/bootstrap-material-datetimepicker/css/bootstrap-material-datetimepicker.css')); ?>"
    rel="stylesheet"
  >
  <link
    href="<?php echo e(asset('assets/plugins/colorpicker/bootstrap-colorpicker.min.css')); ?>"
    rel="stylesheet"
  >

  <style>
    .board-column {
      /* max-width: 21%; */
    }

    .board-column .card {
      box-shadow: none;
    }

    .notify-button {
      /*width: 9em;*/
      height: 1.5em;
      font-size: 0.730rem !important;
      line-height: 0.5 !important;
    }

    .panel-scroll {
      height: calc(100vh - 330px);
      overflow-y: scroll
    }

    .mb-20 {
      margin-bottom: 20px
    }

    .datepicker {
      z-index: 9999 !important;
    }

    .d-block {
      display: block;
    }

    .upcomingdata {
      height: 37.5rem;
      overflow-x: scroll;
    }

    .notify-button {
      height: 1.5em;
      font-size: 0.730rem !important;
      line-height: 0.5 !important;
    }

    .scheduleul {
      padding: 0 15px 0 11px;
    }

    .searchInput {
      width: 50%;
      display: inline
    }

    .searchButton {
      margin-bottom: 4px;
      margin-left: 3px;
    }
  </style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
  <div class="row mb-2">
    <div class="col">
      <a
        class="btn btn-outline btn-success btn-sm toggle-filter"
        href="javascript:;"
        id="toggle-filter"
      >
        <i class="fa fa-sliders"></i> <?php echo app('translator')->get('app.filterResults'); ?>
      </a>
      <a
        class="btn btn-sm btn-primary"
        href="<?php echo e(route('admin.job-applications.table')); ?>"
      >
        <i class="fa fa-table"></i> <?php echo app('translator')->get('app.tableView'); ?>
      </a>
      <a
        class="btn btn-sm btn-info mail_setting"
        href="#"
      >
        <i class="fa fa-envelope-o"></i>
        <?php echo app('translator')->get('modules.applicationSetting.mailSettings'); ?>
      </a>
      
    </div>
    <div class="col-auto">
      <div
        class="form-group pull-right"
        id="search-container"
      >
        <input
          class="form-control"
          id="search"
          name="search"
          placeholder="<?php echo app('translator')->get('modules.jobApplication.enterName'); ?>"
          type="text"
        >
        <a
          class="d-none"
          href="javascript:;"
        >
          <i class="fa fa-times-circle-o"></i>
        </a>
      </div>
    </div>
  </div>

  <div class="container-scroll">
    <div
      class="card"
      id="ticket-filters"
    >
      <div class="card-body">
        <div class="row">
          <div class="col-md-12">
            <h4><?php echo app('translator')->get('app.filterBy'); ?>
              <a
                class="pull-right toggle-filter mr-2 mt-2"
                href="javascript:;"
              >
                <i class="fa fa-times-circle-o"></i>
              </a>
            </h4>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <div class="input-daterange input-group">
                <input
                  <?php if($type == ''): ?> value="<?php echo e($startDate); ?>" <?php endif; ?>
                  class="form-control"
                  id="date-start"
                  name="start_date"
                  type="text"
                >
                <span class="input-group-addon bg-info b-0 p-1 text-white"><?php echo app('translator')->get('app.to'); ?></span>
                <input
                  <?php if($type == ''): ?> value="<?php echo e($endDate); ?>" <?php endif; ?>
                  class="form-control"
                  id="date-end"
                  name="end_date"
                  type="text"
                >
              </div>
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-group">
              <select
                class="select2"
                data-style="form-control"
                id="company"
                name="company"
              >
                <option value="all"><?php echo app('translator')->get('modules.jobApplication.allCompany'); ?></option>
                <?php $__empty_1 = true; $__currentLoopData = $companies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                  <option
                    title="<?php echo e(ucfirst($company->company_name)); ?>"
                    value="<?php echo e($company->id); ?>"
                  ><?php echo e(ucfirst($company->company_name)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <?php endif; ?>
              </select>
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-group">
              <select
                class="select2"
                data-style="form-control"
                id="jobs"
                name="jobs"
              >
                <option value="all"><?php echo app('translator')->get('modules.jobApplication.allJobs'); ?></option>
                <?php $__empty_1 = true; $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                  <option
                    title="<?php echo e(ucfirst($job->title)); ?>"
                    value="<?php echo e($job->id); ?>"
                  ><?php echo e(ucfirst($job->title)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <?php endif; ?>
              </select>
            </div>
          </div>

          <div class="col-md-4">
            <div class="form-group">
              <select
                class="select2"
                data-style="form-control"
                id="location"
                name="location"
              >
                <option value="all"><?php echo app('translator')->get('modules.jobApplication.allLocation'); ?></option>
                <?php $__empty_1 = true; $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                  <option value="<?php echo e($location->id); ?>"><?php echo e(ucfirst($location->location)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <?php endif; ?>

              </select>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <select
                class="select2"
                data-placeholder="Select Skills"
                data-style="form-control"
                id="skill"
                multiple="multiple"
                name="skill[]"
              >
                <?php $__empty_1 = true; $__currentLoopData = $skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                  <option value="<?php echo e($skill->id); ?>"><?php echo e(ucfirst($skill->name)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <?php endif; ?>
              </select>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <select
                class="select2"
                data-style="form-control"
                id="questions"
                name="question"
              >
                <option value="all"><?php echo app('translator')->get('modules.jobApplication.allQuestion'); ?></option>
                <?php $__empty_1 = true; $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                  <option value="<?php echo e($question->id); ?>"><?php echo e(ucfirst($question->question)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <?php endif; ?>
              </select>
            </div>
          </div>

          <div
            class="col-md-6"
            id="question_value"
          >
            <div class="form-group">
              <input
                class="form-control"
                id="question-value"
                name="question_value"
                placeholder="Enter question value"
                type="text"
              >
            </div>
          </div>
          <div class="col-md-12">
            <div class="form-group">
              <button
                class="btn btn-success btn-sm"
                id="apply-filters"
                type="button"
              ><i class="fa fa-check"></i> <?php echo app('translator')->get('app.apply'); ?></button>
              <button
                class="btn btn-info btn-sm"
                id="reset-filters"
                type="button"
              ><i class="fa fa-refresh"></i> <?php echo app('translator')->get('app.reset'); ?></button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row container-row">
    </div>
  </div>
  <?php echo $__env->make('admin.application-setting.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
  
  <div
    aria-hidden="true"
    aria-labelledby="myModalLabel"
    class="modal fade bs-modal-md in"
    id="scheduleDetailModal"
    role="dialog"
  >
    <div
      class="modal-dialog modal-lg"
      id="modal-data-schedule"
    >
      <div class="modal-content">
        <div class="modal-header">
          <button
            aria-hidden="true"
            class="close"
            data-dismiss="modal"
            type="button"
          ></button>
          <span
            class="caption-subject font-red-sunglo bold uppercase"
            id="modelHeading"
          ></span>
        </div>
        <div class="modal-body">
          Loading...
        </div>
        <div class="modal-footer">
          <button
            class="btn default"
            data-dismiss="modal"
            type="button"
          >Close</button>
          <button
            class="btn blue"
            type="button"
          >Save changes</button>
        </div>
      </div>
      <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
  </div>
  

<?php $__env->stopSection(); ?>

<?php $__env->startPush('footer-script'); ?>
  <script src="<?php echo e(asset('assets/plugins/jQueryUI/jquery-ui.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/lobipanel/dist/js/lobipanel.min.js')); ?>"></script>
  <script
    src="<?php echo e(asset('assets/node_modules/moment/moment.js')); ?>"
    type="text/javascript"
  ></script>
  <script src="<?php echo e(asset('assets/node_modules/multiselect/js/jquery.multi-select.js')); ?>"></script>
  <script
    src="<?php echo e(asset('assets/node_modules/bootstrap-datepicker/bootstrap-datepicker.min.js')); ?>"
    type="text/javascript"
  ></script>
  <script
    src="<?php echo e(asset('assets/node_modules/select2/dist/js/select2.full.min.js')); ?>"
    type="text/javascript"
  ></script>
  <script
    src="<?php echo e(asset('assets/node_modules/bootstrap-select/bootstrap-select.min.js')); ?>"
    type="text/javascript"
  ></script>
  <script
    src="<?php echo e(asset('assets/plugins/jquery-bar-rating-master/dist/jquery.barrating.min.js')); ?>"
    type="text/javascript"
  ></script>
  <script
    src="<?php echo e(asset('assets/node_modules/bootstrap-material-datetimepicker/js/bootstrap-material-datetimepicker.js')); ?>"
    type="text/javascript"
  ></script>
  <script src="<?php echo e(asset('assets/plugins/colorpicker/bootstrap-colorpicker.min.js')); ?>"></script>

  <script>
    $(".select2").select2({
      width: '100%'
    });
    //implementation of load more functionality

    loadData();

    $('body').on('click', '.load-more-application', function() {
      var columnId = $(this).data('column-id');
      var totalTasks = $(this).data('total-tasks');
      var currentTotalTasks = $('#drag-container-' + columnId + ' .task-card').length;
      var lastelement = $('#drag-container-' + columnId + ' div.task-card:last');

      var startDate = $('#date-start').val();
      var jobs = $('#jobs').val();
      var location = $('#location').val();
      var skill = $('#skill').val();
      var questions = $('#questions').val();
      var search = $('#search').val();
      var question_value = $('#question-value').val();
      var company = $('#company').val();

      if (startDate == '') {
        startDate = null;
      }

      var endDate = $('#date-end').val();

      if (endDate == '') {
        endDate = null;
      }

      var url = '<?php echo e(route('admin.job-applications.loadMore')); ?>?startDate=' + startDate + '&endDate=' + endDate +
        '&jobs=' + jobs + '&search=' + search + '&location=' +
        location + '&skill=' + skill + '&questions=' + questions + '&question_value=' + question_value +
        '&columnId=' + columnId + '&currentTotalRecords=' + currentTotalTasks +
        '&totalRecord=' + totalTasks + '&company=' + company;

      $.easyAjax({
        url: url,
        container: '#drag-container-' + columnId,
        blockUI: true,
        type: "GET",
        success: function(response) {
          lastelement.after(response.view);

          if (response.load_more == 'show') {
            $('#loadMoreBox' + columnId).show();

          } else {
            $('#loadMoreBox' + columnId).remove();
          }

          $("body").tooltip({
            selector: '[data-toggle="tooltip"]'
          });

          $('.example-fontawesome').barrating({
            theme: 'fontawesome-stars',
            showSelectedRating: false,
            readonly: true,

          });

          $(function() {
            $('.bar-rating').each(function() {
              const val = $(this).data('value');

              $(this).barrating('set', val ? val : '');
            });
          });

          var oldParentId, currentParentId, oldElementIds = [],
            i = 1;

          let draggingTaskId = 0;
          let draggedTaskId = 0;
          let missingElementId = 0;
          let currentApplicationId = 0;

          $('.lobipanel').on('dragged.lobiPanel', function(e, lobiPanel) {
            var $parent = $(this).parent(),
              $children = $parent.children('.show-detail');
            var pr = $(this).closest('.board-column');

            if (draggingTaskId !== 0) {
              oldParentId = pr.data('column-id');
            }
            currentParentId = pr.data('column-id');

            var boardColumnIds = [];
            var applicationIds = [];
            var prioritys = [];

            $children.each(function(ind, el) {
              boardColumnIds.push($(el).closest('.board-column').data('column-id'));
              applicationIds.push($(el).data('application-id'));
              prioritys.push($(el).index());
            });

            if (draggingTaskId !== 0) {
              boardStracture[oldParentId] = [...applicationIds, currentApplicationId];
            } else {
              const result = boardStracture[oldParentId].filter(el => el !== currentApplicationId);
              boardStracture[oldParentId] = result;
              boardStracture[currentParentId] = applicationIds;
            }

            if (oldParentId == 3 && currentParentId == 4) {
              $('#buttonBox' + oldParentId + currentApplicationId).show();
              var button = '<button onclick="sendReminder(' + currentApplicationId +
                ', \'notify\')" type="button" class="btn btn-sm btn-info notify-button"><?php echo app('translator')->get('app.notify'); ?></button>';
              $('#buttonBox' + oldParentId + currentApplicationId).html(button);
              $('#buttonBox' + oldParentId + currentApplicationId).attr('id', 'buttonBox' +
                currentParentId + currentApplicationId);

            } else if (oldParentId == 4 && currentParentId == 3) {
              var timeStamp = $('#buttonBox' + oldParentId + currentApplicationId).data('timestamp');
              var currentDate = <?php echo e($currentDate); ?>;
              if (currentDate < timeStamp) {
                $('#buttonBox' + oldParentId + currentApplicationId).show();
                var button = '<button onclick="sendReminder(' + currentApplicationId +
                  ', \'reminder\')" type="button" class="btn btn-sm btn-info notify-button"><?php echo app('translator')->get('app.reminder'); ?></button>';
                $('#buttonBox' + oldParentId + currentApplicationId).html(button);
              }
              $('#buttonBox' + oldParentId + currentApplicationId).attr('id', 'buttonBox' +
                currentParentId + currentApplicationId);
            } else {
              $('#buttonBox' + oldParentId + currentApplicationId).hide();
              $('#buttonBox' + oldParentId + currentApplicationId).attr('id', 'buttonBox' +
                currentParentId + currentApplicationId);
            }

            var startDate = $('#date-start').val();
            var jobs = $('#jobs').val();
            var search = $('#search').val();

            if (startDate == '') {
              startDate = null;
            }

            var endDate = $('#date-end').val();

            if (endDate == '') {
              endDate = null;
            }
            // update values for all tasks
            $.easyAjax({
              url: '<?php echo e(route('admin.job-applications.updateIndex')); ?>',
              type: 'POST',
              container: '.container-row',
              data: {
                boardColumnIds: boardColumnIds,
                applicationIds: applicationIds,
                prioritys: prioritys,
                startDate: startDate,
                jobs: jobs,
                search: search,
                endDate: endDate,
                draggingTaskId: draggingTaskId,
                draggedTaskId: draggedTaskId,
                '_token': '<?php echo e(csrf_token()); ?>'
              },
              success: function(response) {
                if (draggedTaskId !== 0) {
                  $.each(response.columnCountByIds, function(key, value) {
                    $('#columnCount_' + value.id).html((value.status_count));
                    $('#columnCount_' + value.id).parents('.board-column').find('.lobipanel').css(
                      'border-color', value.color);
                  });
                }
              }
            });
            if (draggingTaskId !== 0) {
              draggedTaskId = draggingTaskId;
              draggingTaskId = 0;
            }
          }).lobiPanel({
            sortable: true,
            reload: false,
            editTitle: false,
            close: false,
            minimize: false,
            unpin: false,
            expand: false,
          });

          var isDragging = 0;
          $('.lobipanel-parent-sortable').on('sortactivate', function() {
            $('.board-column > .panel-body').css('overflow-y', 'unset');
            isDragging = 1;
          });
          $('.lobipanel-parent-sortable').on('sortstop', function(e) {
            $('.board-column > .panel-body').css('overflow-y', 'auto');
            isDragging = 0;
          });

          $('.show-detail').click(function(event) {
            if ($(event.target).hasClass('notify-button')) {
              return false;
            }
            var id = $(this).data('application-id');
            draggingTaskId = currentApplicationId = id;

            if (isDragging == 0) {
              $(".right-sidebar").slideDown(50).addClass("shw-rside");

              var url = "<?php echo e(route('admin.job-applications.show', ':id')); ?>";
              url = url.replace(':id', id);

              $.easyAjax({
                type: 'GET',
                url: url,
                success: function(response) {
                  if (response.status == "success") {
                    $('#right-sidebar-content').html(response.view);
                  }
                }
              });
            }
          })
        }
      });
    });

    //filters
    $('#apply-filters').click(function() {
      loadData();
    });

    //reset filters
    $('#reset-filters').click(function() {
      $('#date-start').val('<?php echo e($startDate); ?>');
      $('#date-end').val('<?php echo e($endDate); ?>');
      $('#jobs').val('all').trigger('change');
      $('#company').val('all').trigger('change');
      $('#location').val('all').trigger('change');
      loadData();
    })

    //apply search
    $('#applySearch').click(function() {
      var search = $('#search').val();
      if (search !== undefined && search !== null && search !== "") {
        loadData();
      }
    })

    $('#date-end').bootstrapMaterialDatePicker({
      weekStart: 0,
      time: false
    });
    $('#date-start').bootstrapMaterialDatePicker({
      weekStart: 0,
      time: false
    }).on('change', function(e, date) {
      $('#date-end').bootstrapMaterialDatePicker('setMinDate', date);
    });

    // Create application status modal view
    function createApplicationStatus() {
      var url = "<?php echo e(route('admin.application-status.create')); ?>";

      $('#modelHeading').html('Application Status');
      $.ajaxModal('#scheduleDetailModal', url);
    }

    function deleteStatus(id) {
      const panels = $('.board-column[data-column-id="' + id + '"').find('.lobipanel.show-detail');
      let applicationIds = [];
      panels.each((ind, element) => {
        applicationIds.push($(element).data('application-id'));
      });

      swal({
        title: "<?php echo app('translator')->get('errors.areYouSure'); ?>",
        text: "<?php echo app('translator')->get('errors.deleteStatusWarning'); ?>",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: "<?php echo app('translator')->get('app.delete'); ?>",
        cancelButtonText: "<?php echo app('translator')->get('app.cancel'); ?>",
        closeOnConfirm: true,
        closeOnCancel: true
      }, function(isConfirm) {
        if (isConfirm) {
          let url = "<?php echo e(route('admin.application-status.destroy', ':id')); ?>";
          url = url.replace(':id', id);

          let data = {
            _token: '<?php echo e(csrf_token()); ?>',
            _method: 'DELETE',
            applicationIds: applicationIds
          }

          $.easyAjax({
            url,
            data,
            type: 'POST',
            container: '.container-row',
            success: function(response) {
              if (response.status == 'success') {
                loadData();
              }
            }
          })
        }
      });
    }

    function editStatus(id) {
      var url = "<?php echo e(route('admin.application-status.edit', ':id')); ?>";
      url = url.replace(':id', id);

      $('#modelHeading').html('Application Status');
      $.ajaxModal('#scheduleDetailModal', url);
    }

    function saveStatus() {
      $.easyAjax({
        url: "<?php echo e(route('admin.application-status.store')); ?>",
        container: '#createStatus',
        type: "POST",
        data: $('#createStatus').serialize(),
        success: function(response) {
          $('#scheduleDetailModal').modal('hide');
          loadData();
        }
      });
    }

    function updateStatus(id) {
      let url = "<?php echo e(route('admin.application-status.update', ':id')); ?>";
      url = url.replace(':id', id);

      $.easyAjax({
        url: url,
        container: '#updateStatus',
        type: "POST",
        data: $('#updateStatus').serialize(),
        success: function(response) {
          $('#scheduleDetailModal').modal('hide');
          loadData();
        }
      });
    }

    function loadData() {
      var startDate = $('#date-start').val();
      var jobs = $('#jobs').val();
      var location = $('#location').val();
      var skill = $('#skill').val();
      var questions = $('#questions').val();
      var search = $('#search').val();
      var question_value = $('#question-value').val();
      var company = $('#company').val();
      if (startDate == '') {
        startDate = null;
      }

      var endDate = $('#date-end').val();

      if (endDate == '') {
        endDate = null;
      }

      var url = '<?php echo e(route('admin.job-applications.index')); ?>?startDate=' + startDate + '&endDate=' + endDate + '&jobs=' +
        jobs + '&search=' + search + '&location=' + location + '&skill=' + skill + '&questions=' + questions +
        '&question_value=' + question_value + '&company=' + company;

      $.easyAjax({
        url: url,
        container: '.container-row',
        type: "GET",
        success: function(response) {
          $('.container-row').html(response.view);
        }

      })
    }

    search($('#search'), 500, 'data');

    $('.toggle-filter').click(function() {
      $('#ticket-filters').toggle('slide');
    });

    $('#question_value').hide();
    $('#questions').change(function() {
      $('#question_value').show();
    });

    //click mail setting open modal
    $(document).on('click', '.mail_setting', function() {
      var data1 = '';
      $.ajax({
        url: "<?php echo e(route('admin.application-setting.create')); ?>",
        success: function(data) {
          data1 = eval(data.mail_setting);
          var options = '';
          $.each(data1, function(name, status) {
            if (status.status == true) {
              options +=
                '<input type="checkbox"  checked style=" style="text-align: center; margin: 6px 15px 13px 0px;" name="checkBoardColumn[]" id="checkbox-' +
                name + '" value="' + name + '"  />';
              options += '<label for="checkbox-' + name +
                '" style="text-align: center; margin: 6px 15px 13px 0px;">' + status.name + '</label>';
            } else {
              options +=
                '<input type="checkbox" style="text-align: center; margin: 6px 10px 4px 0px;" class = "iCheck-helper" name="checkBoardColumn[]" id="checkbox-' +
                name + '" value="' + name + '"  />';
              options += '<label for="checkbox-' + name +
                '" style="text-align: center; margin: 6px 10px 4px 0px;">' + status.name + '</label>';
            }
          });
          $('#assetNameMenu').html(options);
          $('#legal_term').val(data.legal_term);
          $('#ModalLoginForm').modal('show');
          return false;
        }

      });

    });

    $('#company').change(function() {
      var company_id = $(this).val();
      $.ajax({
        url: "<?php echo e(route('admin.job-applications.get-jobs')); ?>",
        type: "GET",
        data: {
          'companyId': company_id,
        },
        success: function(data) {
          $('#jobs').html(data.jobs);
        },
      });
    });
  </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/job-applications/board.blade.php ENDPATH**/ ?>