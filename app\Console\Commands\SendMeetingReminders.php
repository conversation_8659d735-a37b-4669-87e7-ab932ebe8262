<?php

namespace App\Console\Commands;

use App\ZoomMeeting;
use App\Notifications\MeetingReminder;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendMeetingReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meetings:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send meeting reminders to attendees';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Obtener reuniones programadas para mañana
        $tomorrow = Carbon::tomorrow();
        
        $meetings = ZoomMeeting::with(['attendees'])
            ->whereDate('start_date_time', $tomorrow)
            ->get();

        $count = 0;
        foreach ($meetings as $meeting) {
            if ($meeting->attendees) {
                foreach ($meeting->attendees as $attendee) {
                    $attendee->notify(new MeetingReminder($meeting));
                    $count++;
                }
            }
        }

        $this->info("Se enviaron {$count} recordatorios de reunión.");
        
        return 0;
    }
}
