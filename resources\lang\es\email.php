<?php

return  [
    // 1. Nueva solicitud de empleo recibida (para administradores)
    "newJobApplication" => [
        "subject" => "Nueva solicitud de empleo recibida",
        "text" => "Una nueva solicitud de empleo ha sido enviada. El candidato {candidate_name} ha aplicado para el puesto {job_title}.",
        "body" => "Una nueva solicitud de empleo ha sido enviada. El candidato {candidate_name} ha aplicado para el puesto {job_title}.",
    ],

    // 2. Solicitud de empleo recibida (confirmación al candidato)
    "applicationReceived" => [
        "subject" => "Solicitud de empleo recibida",
        "text" => "Gracias por postular a {company_name}. Te confirmamos que hemos recibido correctamente tu solicitud de empleo para el puesto de {job_title}. Nuestro equipo de selección revisará tu perfil y, en caso de avanzar a una siguiente etapa, nos estaremos comunicando contigo a la brevedad. Agradecemos tu interés en formar parte de nuestra organización.",
        "body" => "Estimado/a {candidate_name}:\n\nGracias por postular a {company_name}.\nTe confirmamos que hemos recibido correctamente tu solicitud de empleo para el puesto de {job_title}.\n\nNuestro equipo de selección revisará tu perfil y, en caso de avanzar a una siguiente etapa, nos estaremos comunicando contigo a la brevedad.\n\nAgradecemos tu interés en formar parte de nuestra organización.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 3. Oferta de trabajo enviada
    "jobOffer" => [
        "subject" => "Has recibido una oferta de trabajo",
        "text" => "¡Tenemos buenas noticias! Nos complace informarte que has sido seleccionado/a para continuar en nuestro proceso y hemos preparado una oferta de trabajo para ti.",
        "body" => "Estimado/a {candidate_name}:\n\n¡Tenemos buenas noticias!\n\nNos complace informarte que has sido seleccionado/a para continuar en nuestro proceso y hemos preparado una oferta de trabajo para ti.\n\nAdjunto a este mensaje encontrarás los detalles de la propuesta, la cual incluye el cargo ofrecido, condiciones contractuales y beneficios asociados.\n\nTe invitamos a revisar cuidadosamente la información y responder en el plazo indicado. Si tienes alguna consulta adicional, no dudes en escribirnos.\n\nGracias por tu interés en ser parte de {company_name}.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 4. Oferta de trabajo aceptada
    "jobOfferAccepted" => [
        "subject" => "Oferta de trabajo aceptada",
        "text" => "¡Gracias por aceptar nuestra oferta de trabajo! Estamos muy entusiasmados de que te unas al equipo de {company_name}.",
        "body" => "Estimado/a {candidate_name}:\n\n¡Gracias por aceptar nuestra oferta de trabajo!\n\nEstamos muy entusiasmados de que te unas al equipo de {company_name}.\n\nNos estaremos comunicando contigo para brindarte los detalles del proceso de incorporación y los pasos previos a tu ingreso (firma de documentos, entrega de información personal, inducción, entre otros).\n\nSi tienes alguna consulta o necesitas asistencia, puedes comunicarte con nosotros a través de este mismo medio.\n\nBienvenido/a a esta nueva etapa profesional.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 5. Oferta de trabajo rechazada
    "jobOfferRejected" => [
        "subject" => "Oferta de trabajo rechazada",
        "text" => "Hemos recibido tu decisión de no aceptar la oferta de trabajo para el puesto de {job_title}.",
        "body" => "Estimado/a {candidate_name}:\n\nHemos recibido tu decisión de no aceptar la oferta de trabajo para el puesto de {job_title}.\n\nLamentamos que en esta ocasión no hayas podido sumarte a nuestro equipo, pero agradecemos el interés y el tiempo que dedicaste a participar en nuestro proceso de selección.\n\nEsperamos contar contigo en futuras oportunidades y te deseamos mucho éxito en tus próximos desafíos profesionales.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 6. Entrevista programada
    "interviewSchedule" => [
        "subject" => "Entrevista programada",
        "text" => "Tu entrevista para el puesto {job_title} ha sido programada para el {date} a las {time}.",
        "body" => "Estimado/a {candidate_name}:\n\nTu entrevista para el puesto {job_title} ha sido programada para el {date} a las {time}.\n\nUbicación: {location}\n\nPor favor, confirma tu asistencia y llega puntualmente.\n\nAtentamente,\nEquipo de Gestión Humana",
        "hiredNotify" => "Notificación de contratación",
        "response" => "Para aceptar o rechazar",
        "hiredText" => "Has sido contratado para el requerimiento",
        "interviewReminder" => "Recordatorio de entrevista",
        "employeeResponse" => "tiene :type programado para el requerimiento :job",
        "candidate" => "Nombre del candidato: ",
        "interviewOn" => "La entrevista es el",
        "scheduleResponse" => "Respuesta del empleado sobre la programación de la entrevista.",
        "scheduleStatus" => "El estado de la entrevista ha cambiado a",
    ],

    // 7. Recordatorio de entrevista
    "interviewReminder" => [
        "subject" => "Recordatorio de entrevista",
        "text" => "Este es un recordatorio de tu próxima entrevista para el puesto {job_title} programada para el {date} a las {time}.",
        "body" => "Estimado/a {candidate_name}:\n\nEste es un recordatorio de tu próxima entrevista para el puesto {job_title} programada para el {date} a las {time}.\n\nUbicación: {location}\n\nPor favor, llega puntualmente.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 8. Nueva alerta de trabajo
    "newJobAlert" => [
        "subject" => "Nueva oferta de trabajo disponible",
        "text" => "Tenemos una nueva oportunidad laboral que coincide con tus intereses y preferencias registradas en nuestro sistema.",
        "body" => "Estimado/a {candidate_name}:\n\nTenemos una nueva oportunidad laboral que coincide con tus intereses y preferencias registradas en nuestro sistema.\n\nPuesto disponible: {job_title}\nUbicación: {location}\nTipo de contrato: {contract_type}\n\nSi estás interesado/a, puedes revisar los detalles y postular directamente desde el siguiente enlace:\n\n👉 {job_link}\n\n¡No dejes pasar esta oportunidad de seguir creciendo con nosotros!\n\nAtentamente,\nEquipo de Gestión Humana",
        "applyNow" => "Postúlate ahora",
    ],

    // 9. Estado del candidato actualizado
    "candidateStatusUpdate" => [
        "subject" => "Actualización de estado del candidato",
        "text" => "Te informamos que tu estado en el proceso de selección para el puesto de {job_title} ha sido actualizado.",
        "body" => "Estimado/a {candidate_name}:\n\nTe informamos que tu estado en el proceso de selección para el puesto de {job_title} ha sido actualizado.\n\n📌 Nuevo estado: {status}\n\nNuestro equipo de selección continuará evaluando tu perfil y te mantendremos informado/a ante cualquier avance o requerimiento adicional.\n\nAgradecemos tu interés en formar parte de {company_name} y tu disposición durante este proceso.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 10. Nueva reunión creada
    "newMeeting" => [
        "subject" => "Nueva reunión creada",
        "text" => "Se ha programado una nueva reunión.",
        "body" => "Se ha programado una nueva reunión.\n\nDetalles de la reunión:\nTítulo: {meeting_title}\nFecha: {date}\nHora: {time}\nUbicación: {location}\n\nPor favor, confirma tu asistencia.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 11. Recordatorio de reunión
    "meetingReminder" => [
        "subject" => "Recordatorio de reunión",
        "text" => "Este es un recordatorio de tu próxima reunión: {meeting_title}, programada para el {date} a las {time}.",
        "body" => "Este es un recordatorio de tu próxima reunión:\n\nTítulo: {meeting_title}\nFecha: {date}\nHora: {time}\nUbicación: {location}\n\nPor favor, llega puntualmente.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // 12. Correo de prueba SMTP
    "smtpTest" => [
        "subject" => "Correo de prueba SMTP",
        "text" => "Este es un correo de prueba para verificar la configuración SMTP.",
        "body" => "Este es un correo de prueba para verificar la configuración SMTP del sistema.",
    ],

    // 13. Agradecimiento por participación (candidato rechazado)
    "thankYouParticipation" => [
        "subject" => "Agradecimiento",
        "text" => "Gracias por tu interés en el puesto de {job_title} y por el tiempo que dedicaste a participar en nuestro proceso de selección.",
        "body" => "Estimado/a {candidate_name}:\n\nGracias por tu interés en el puesto de {job_title} y por el tiempo que dedicaste a participar en nuestro proceso de selección.\n\nDespués de revisar cuidadosamente todas las candidaturas y evaluar cada perfil en función de los requisitos del puesto, lamentamos informarte que en esta ocasión hemos decidido continuar con otros/as candidatos/as cuya experiencia y habilidades se ajustan de forma más precisa a lo que estamos buscando en este momento.\n\nAgradecemos sinceramente tu interés en formar parte de {company_name} y te animamos a mantenerte al tanto de futuras oportunidades que puedan alinearse con tu perfil.\n\nTe deseamos mucho éxito en tus proyectos profesionales.\n\nAtentamente,\nEquipo de Gestión Humana",
    ],

    // Elementos adicionales para compatibilidad
    "loginDashboard" => "Iniciar sesión en el panel",
    "thankyouNote" => "¡Gracias!",
    "hello" => "Hola",
    "your" => "Tu",
    "on" => "En",
    "interviewOn" => "La entrevista es el",
    "viewOffer" => "Ver oferta de trabajo",
    "newUser" => [
        "text" => "Los detalles de inicio de sesión son",
        "subject" => "Cuenta creada con éxito",
    ],
    "interviewScheduleStatus" => [
        "subject" => "El estado de la programación de la entrevista ha cambiado.",
        "text" => "entrevista programada para el requerimiento",
        "statusChangesTo" => "el estado cambia a",
    ],
    "ScheduleStatusCandidate" => [
        "subject" => "Respuesta de la programación de la entrevista.",
        "text" => "entrevista programada para el requerimiento",
        "hasBeen" => "ha sido",
        "forCheckDetails" => "Para verificar detalles",
        "nowStatus" => "Ahora tu estado es",
    ],
    "newJobOpening" => [
        "text" => "Nos gustaría invitarte a postularte para una vacante de trabajo.",
        "jobTitle" => "Título del requerimiento",
        "jobLocation" => "Ubicación",
        "moreDetails" => "Visita el siguiente enlace para más detalles y postúlate para el trabajo.",
        "jobDetails" => "POSTULARSE PARA EL TRABAJO",
        "subject" => "Nueva oferta de trabajo",
    ],
];
