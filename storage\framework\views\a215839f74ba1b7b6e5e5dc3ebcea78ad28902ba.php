
<style>
  .header.inner-header {
    height: 435px;
  }

  @media (max-width:767px) {
    .header.inner-header {
      height: 224px;
    }
  }

  .required:after {
    content: " *";
    color: red;
  }

  .has-error .form-control {
    border-color: #ff0000;
  }

  .margin-left {
    margin-left: 100px;
  }
</style>
<?php $__env->startSection('header-text'); ?>
  <h1 class="hidden-sm-down fs-40 mb-10 text-white"><?php echo e(ucwords($job->title)); ?></h1>
  <h3 class="hidden-sm-up mb-10 text-white"><?php echo e(ucwords($job->title)); ?></h3>
  <div class="text-white">
    <a class="text-white" href="<?php echo e(route('jobs.jobOpenings')); ?>"><u><?php echo app('translator')->get('modules.front.jobOpenings'); ?></u>&nbsp; </a> &raquo; &nbsp;<span
          class="text-white"><?php echo e(ucwords($job->title)); ?></span>
  </div>
<?php $__env->stopSection(); ?>
<!-- <?php $__env->startSection('header-text'); ?>
  <h1 class="hidden-sm-down"><?php echo e(ucwords($job->title)); ?></h1>
          <h5 class="hidden-sm-down"><i class="icon-map-pin"></i> <?php echo e(ucwords($location->location)); ?></h5>
<?php $__env->stopSection(); ?> -->

<?php $__env->startPush('header-css'); ?>
  <link href="<?php echo e(asset('assets/plugins/datepicker/datepicker3.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/plugins/select2/select2.min.css')); ?>" rel="stylesheet">
  <link href="<?php echo e(asset('assets/node_modules/switchery/dist/switchery.min.css')); ?>" rel="stylesheet">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
  <?php
    $gender = [
        'male' => __('modules.front.male'),
        'female' => __('modules.front.female'),
        'others' => __('modules.front.others'),
    ];
  ?>

  <form id="createForm" method="POST">
    <?php echo csrf_field(); ?>
    <input name="job_id" type="hidden" value="<?php echo e($job->id); ?>">
    <input <?php if(isset($location->id)): ?> value="<?php echo e($location->id); ?>" <?php else: ?> value="" <?php endif; ?> name="location_id" type="hidden">
    <div class="container">
      <div class="row gap-y">
        <div class="col-md-4 pb-30 mt-50 px-20">
          <h5 class="required"><?php echo app('translator')->get('modules.front.personalInformation'); ?></h5>
        </div>
        <div class="col-md-8 pb-30 mt-50">
          <div class="row">
            <div class="col-md-6 form-group">
              <input class="form-control form-control-lg" name="first_name" placeholder="<?php echo app('translator')->get('modules.front.firstName'); ?>" type="text"
                value="<?php if($user): ?> <?php echo e($user->first_name); ?> <?php endif; ?>">
            </div>
            <div class="col-md-6 form-group">
              <input class="form-control form-control-lg" name="last_name" placeholder="<?php echo app('translator')->get('modules.front.lastName'); ?>" type="text"
                value="<?php if($user): ?> <?php echo e($user->last_name); ?> <?php endif; ?>">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 form-group">
              <input class="form-control form-control-lg" name="dni" placeholder="DNI" type="text"
                value="<?php if($user): ?> <?php echo e($user->dni); ?> <?php endif; ?>">
            </div>
            <div class="col-md-6 form-group">
              <input class="form-control form-control-lg" name="email" placeholder="<?php echo app('translator')->get('modules.front.email'); ?>" type="email"
                value="<?php if($user): ?> <?php echo e($user->email); ?> <?php endif; ?>">
            </div>
          </div>
          <div class="form-group">
            <input class="form-control form-control-lg" name="phone" placeholder="<?php echo app('translator')->get('modules.front.phone'); ?>" type="tel"
              value="<?php if($user): ?> <?php echo e($user->phone); ?> <?php endif; ?>">
          </div>
          <label class="control-label" class="required"><h6>Lugar de residencia</h6></label>
          <div class="row">
            <div class="col-md-6 form-group">
              <select class="form-control form-control-lg select2" name="departamento_id" id="departamento_id">
                <option value="">Seleccione el departamento</option>
                <?php $__currentLoopData = $departamentos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $departamento): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($departamento->id); ?>"><?php echo e($departamento->departamento); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
            <div class="col-md-6 form-group">
              <select class="form-control form-control-lg select2" name="provincia_id" id="provincia_id">
                <option value="">Seleccione la provincia</option>
              </select>
            </div>
          </div>
          <label class="control-label" class="required"><h6><?php echo app('translator')->get('modules.front.gender'); ?></h6></label>
          <div class="form-group">
            <div class="form-inline">
              <?php $__currentLoopData = $gender; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" id="<?php echo e($key); ?>" name="gender" type="radio" value="<?php echo e($key); ?>">
                  <label class="form-check-label" for="<?php echo e($key); ?>"><?php echo e(ucFirst($value)); ?></label>
                </div>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
          </div>
          <div class="form-group">
            <input autocomplete="none" class="form-control form-control-lg dob" name="dob" placeholder="<?php echo app('translator')->get('modules.front.dob'); ?>" type="text">
          </div>
        </div>
        <div class="col-md-4 pb-30 bt-1 px-20">
          <h5><?php echo app('translator')->get('modules.front.photo'); ?></h5>
        </div>
        <div class="col-md-8 py-30 bt-1">
          <div class="form-group">
            <img src="<?php if($user): ?> <?php echo e($user->avatar); ?> <?php endif; ?>">
            <input type="hidden" name="linkedinPhoto" value="<?php if($user): ?> <?php echo e($user->avatar); ?> <?php endif; ?>">
            <?php if($user): ?>
                <input type="hidden" name="apply_type" value="linkedin">
            <?php endif; ?>
            <input class="select-file" accept=".png,.jpg,.jpeg,.webp" type="file" name="photo"><br>
            <span><?php echo app('translator')->get('modules.front.imageFileType'); ?></span>
          </div>
        </div>        
        <div class="col-md-4 pb-30 bt-1 px-20">
          <h5 class="required"><?php echo app('translator')->get('modules.front.resume'); ?></h5>
        </div>
        <div class="col-md-8 py-30 bt-1">
          <div class="form-group">
            <input accept=".png,.jpg,.jpeg,.pdf,.doc,.docx,.xls,.xlsx,.rtf" class="select-file" name="resume" type="file"><br>
            <span><?php echo app('translator')->get('modules.front.resumeFileType'); ?></span>
          </div>
        </div>
        <div class="col-md-4 pb-30 bt-1 px-20">
          <h5 class="required"><?php echo app('translator')->get('modules.front.certificado'); ?></h5>
        </div>
        <div class="col-md-8 py-30 bt-1">
          <div class="form-group">
            <input accept=".png,.jpg,.jpeg,.pdf,.doc,.docx,.xls,.xlsx,.rtf" class="select-file" name="certificado" type="file"><br>
            <span><?php echo app('translator')->get('modules.front.resumeFileType'); ?></span>
          </div>
        </div>        
        <?php if(count($jobQuestion) > 0): ?>
          <div class="col-md-4 pb-30 bt-1 px-20">
            <h5><?php echo app('translator')->get('modules.front.additionalDetails'); ?></h5>
          </div>

          <div class="col-md-8 pb-30 bt-1">
            <?php $__empty_1 = true; $__currentLoopData = $jobQuestion; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
              <div class="form-group">
                <label class="control-label" for="answer[<?php echo e($question->id); ?>]"><?php echo e($question->question); ?></label><br>
                <?php if($question->type == 'text'): ?>
                  <input class="form-control form-control-lg" id="answer[<?php echo e($question->id); ?>]" name="answer[<?php echo e($question->id); ?>]"
                         placeholder="<?php echo app('translator')->get('modules.front.yourAnswer'); ?>" type="text">
                <?php else: ?>
                  <input name="answer[<?php echo e($question->id); ?>]" type="hidden">
                  <input accept=".png,.jpg,.jpeg,.pdf,.doc,.docx,.xls,.xlsx,.rtf" class="select-file" id="answer[<?php echo e($question->id); ?>]"
                         name="answer[<?php echo e($question->id); ?>]" type="file"><br>
                  <span><?php echo app('translator')->get('modules.front.resumeFileType'); ?></span>
                <?php endif; ?>
              </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <?php endif; ?>
          </div>
        <?php endif; ?>
        <div class="col-md-4 pb-30 bt-1 px-20">
          <h5 class="required"><?php echo app('translator')->get('modules.front.legalTerm'); ?></h5>
        </div>
        <div class="col-md-8 py-30 bt-1">
          <div class="form-group">
            <div class="form-control form-control-lg legal-term">
              <?php echo $applicationSetting->legal_term; ?>

            </div>
          </div>
          <div class="form-group mt-30">
            <div class="switchery-demo d-inline-flex mr-20">
              <input class="js-switch clearfix float-right col" data-color="#00c292" data-size="small" id="agree_term" name="term_agreement" type="checkbox" value="yes" />
              <label class="align-top col" for="term_agreement"><b><?php echo app('translator')->get('modules.front.agreeWithTerm'); ?></b></label>
            </div>
          </div>
          <div class="form-group mt-30">
            <div class="switchery-demo d-inline-flex mr-20">
              <input class="js-switch clearfix float-right col" data-color="#00c292" data-size="small" id="agree_truth" name="truth_agreement" type="checkbox" value="yes" />
              <label class="align-top col" for="term_agreement"><b><?php echo app('translator')->get('modules.front.agreeWithTruth'); ?></b></label>
            </div>
          </div>
        </div>
        <div class="col-md-12 pb-30">
          <div class="row">
            <?php if($credentials->job_apply_page == 'active' && $credentials->status == 'active'): ?>
              <div class="col-md-8 offset-md-4">
                <div id="captcha_container"></div>
              </div>
            <?php endif; ?>
            <div class="form-group col-md-8 offset-md-4">
              <input class="form-control" id="recaptcha" name="recaptcha" type="hidden">
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-12 pb-30">
        <div class="row">
          <div class="col-md-8 offset-md-4 margin-left">
            <button class="btn btn-lg btn-primary btn-block theme-background" id="save-form" type="button"><?php echo app('translator')->get('modules.front.submitApplication'); ?></button>
          </div>
        </div>
      </div>
    </div>
    </div>
  </form>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('footer-script'); ?>
  <script src="<?php echo e(asset('assets/plugins/datepicker/bootstrap-datepicker.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/plugins/select2/select2.full.min.js')); ?>"></script>
  <script src="<?php echo e(asset('assets/node_modules/switchery/dist/switchery.min.js')); ?>"></script>

  <?php if($credentials->v2_status == 'active' && $credentials->status == 'active'): ?>
    <script src="https://www.google.com/recaptcha/api.js?onload=onloadCallback&render=explicit" async defer></script>
    <script>
      var gcv3;
      var onloadCallback = function() {
        // Renders the HTML element with id 'captcha_container' as a reCAPTCHA widget.
        // The id of the reCAPTCHA widget is assigned to 'gcv3'.
        gcv3 = grecaptcha.render('captcha_container', {
          'sitekey': '<?php echo e($credentials->v2_site_key); ?>',
          'theme': 'light',
          'callback': function(response) {
            if (response) {
              $('#recaptcha').val(response);
            }
          },
        });
      };
    </script>
  <?php endif; ?>

  <?php if($credentials->v3_status == 'active' && $credentials->status == 'active'): ?>
    <script src="https://www.google.com/recaptcha/api.js?render=<?php echo e($credentials->v3_site_key); ?>"></script>
    <script>
      grecaptcha.ready(function() {
        grecaptcha.execute('<?php echo e($credentials->v3_site_key); ?>', {
          action: 'contact'
        }).then(function(token) {
          if (token) {
            document.getElementById('recaptcha').value = token;
          }
        });
      });
    </script>
  <?php endif; ?>

  <script>
    // Switchery
    var elems = Array.prototype.slice.call(document.querySelectorAll('.js-switch'));
    $('.js-switch').each(function() {
      new Switchery($(this)[0], $(this).data());
    });
  </script>
  <script>
    const fetchCountryState = "<?php echo e(route('jobs.fetchCountryState')); ?>";
    const csrfToken = "<?php echo e(csrf_token()); ?>";
    const selectCountry = "<?php echo app('translator')->get('modules.front.selectCountry'); ?>";
    const selectState = "<?php echo app('translator')->get('modules.front.selectState'); ?>";
    const selectCity = "<?php echo app('translator')->get('modules.front.selectCity'); ?>";
    const pleaseWait = "<?php echo app('translator')->get('modules.front.pleaseWait'); ?>";

    let country = "";
    let state = "";
  </script>
  <script src="<?php echo e(asset('front/assets/js/location.js')); ?>"></script>
  <script>
    const departamentos = <?php echo json_encode($departamentos); ?>;
    const provincias = <?php echo json_encode($provincias->groupBy('departamento_id')); ?>;
    $('#departamento_id').change(function() {
      const departamentoId = $(this).val();
      const provinciasSelect = $('#provincia_id');
      provinciasSelect.empty();
      provinciasSelect.append('<option value="">Seleccione la provincia</option>');
      if (departamentoId) {
        $.each(provincias[departamentoId], function(index, provincia) {
          provinciasSelect.append('<option value="' + provincia.id + '">' + provincia.provincia + '</option>');
        });
      }
    });
    $('.dob').datepicker({
      autoclose: true,
      format: 'yyyy-mm-dd',
      endDate: (new Date()).toDateString(),
    });


    $('.select2').select2({
      width: '100%'
    });

    $('.form-group span.select2.select2-container').addClass('form-control form-control-lg');

    $('#save-form').click(function() {
      $.easyAjax({
        url: '<?php echo e(route('jobs.saveApplication')); ?>',
        container: '#createForm',
        type: "POST",
        file: true,
        redirect: true,
        success: function(response) {
          if (response.status == 'success') {
            var successMsg = '<div class="alert alert-success my-100" role="alert">' +
              response.msg +
              ' <a class="" href="<?php echo e(route('jobs.jobOpenings')); ?>"><?php echo app('translator')->get('app.view'); ?> <?php echo app('translator')->get('modules.front.jobOpenings'); ?> <i class="fa fa-arrow-right"></i></a>'
            '</div>';
            $('.main-content .container').html(successMsg);
          }
        },
        error: function(response) {
          handleFails(response);
        }
      })
    });

    function handleFails(response) {
      if (typeof response.responseJSON.errors != "undefined") {
        var keys = Object.keys(response.responseJSON.errors);
        $('#createForm').find(".invalid-feedback").remove();
        $('#createForm').find(".is-invalid").removeClass("is-invalid");

        for (var i = 0; i < keys.length; i++) {
          // Escape dot that comes with error in array fields
          var key = keys[i].replace(".", '\\.');
          var formarray = keys[i];

          // If the response has form array
          if (formarray.indexOf('.') > 0) {
            var array = formarray.split('.');
            response.responseJSON.errors[keys[i]] = response.responseJSON.errors[keys[i]];
            key = array[0] + '[' + array[1] + ']';
          }

          var ele = $('#createForm').find("[name='" + key + "']");

          var grp = ele.closest(".form-group");
          $(grp).find(".help-block").remove();

          //check if wysihtml5 editor exist
          var wys = $(grp).find(".wysihtml5-toolbar").length;

          if (wys > 0) {
            var helpBlockContainer = $(grp);
          } else {
            var helpBlockContainer = $(grp).find("div:first");
          }
          if ($(ele).is(':radio')) {
            helpBlockContainer = $(grp);
          }

          if (helpBlockContainer.length == 0) {
            helpBlockContainer = $(grp);
          }

          helpBlockContainer.append('<div class="help-block">' + response.responseJSON.errors[keys[i]] +
            '</div>');
          $(grp).addClass("has-error");
        }

        if (keys.length > 0) {
          var element = $("[name='" + keys[0] + "']");
          if (element.length > 0) {
            $("html, body").animate({
              scrollTop: element.offset().top - 150
            }, 200);
          }
        }
      }
    }
  </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.front', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/front/job-apply.blade.php ENDPATH**/ ?>