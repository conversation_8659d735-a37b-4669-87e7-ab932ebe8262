<link
  href="<?php echo e(asset('assets/plugins/jquery-bar-rating-master/dist/themes/fontawesome-stars.css')); ?>"
  rel="stylesheet"
>
<script src="<?php echo e(asset('assets/node_modules/switchery/dist/switchery.min.js')); ?>" type="text/javascript"></script>
<link rel="stylesheet" href="<?php echo e(asset('assets/node_modules/switchery/dist/switchery.min.css')); ?>">

<style>
  .right-panel-box {
    overflow-x: scroll;
    max-height: 34rem;
  }

  table.dataTable {
    max-width: 100% !important;
    width: 100% !important;
  }

  .resume-button {
    text-align: center;
    margin-top: 1rem
  }
</style>
<div class="rpanel-title"> <?php echo app('translator')->get('menu.jobApplications'); ?> <span><i class="ti-close right-side-toggle"></i></span></div>
<div class="r-panel-body p-3">
  <div class="row font-12">
    <div class="col-4">
      <img
        class="img-circle img-fluid m-auto d-block"
        src="<?php echo e($application->photo_url); ?>"
      >

      
      <p
        class="text-muted resume-button mr-6"
        id="resume-<?php echo e($application->id); ?>"
      >
        <?php if($application->resume_url): ?>
          <a
            class="btn btn-sm btn-primary"
            href="<?php echo e($application->resume_url); ?>"
            target="_blank"
          >
            <?php echo app('translator')->get('app.view'); ?> <?php echo app('translator')->get('modules.jobApplication.resume'); ?>
          </a>
        <?php endif; ?>
      </p>
      
      <?php if($user->cans('edit_job_applications')): ?>
        <div class="stars stars-example-fontawesome text-center">
          <select
            autocomplete="off"
            id="example-fontawesome"
            name="rating"
          >
            <option value=""></option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
          </select>
        </div>
      <?php endif; ?>
      <?php if($application->status->status == 'oferta de trabajo' && is_null($application->onboard)): ?>
        <p class="text-muted resume-button">
          <a
            class="btn btn-sm btn-success"
            href="<?php echo e(route('admin.job-onboard.create')); ?>?id=<?php echo e($application->id); ?>"
          ><?php echo app('translator')->get('app.startOnboard'); ?></a>
        </p>
      <?php endif; ?>
      <?php if($user->cans('delete_job_applications')): ?>
        <div class="text-muted resume-button">
          <a
            class="btn btn-sm btn-info"
            href="javascript:archiveApplication(<?php echo e($application->id); ?>)"
          >
            <?php echo app('translator')->get('modules.jobApplication.archiveApplication'); ?>
          </a>
        </div>
      <?php endif; ?>
      <?php if($user->cans('delete_job_applications') && $application->status->status != 'rechazado'): ?>
        <div class="text-muted resume-button">
          <a
            class="btn btn-sm btn-danger"
            href="javascript:rejectApplication(<?php echo e($application->id); ?>)"
          >
            <?php echo app('translator')->get('modules.jobApplication.rejectApplication'); ?>
          </a>
        </div>
      <?php endif; ?>
      <?php if($user->cans('add_schedule') && $application->status->status == 'entrevistas'): ?>
        <div class="text-muted resume-button">
          <a
            class="btn btn-sm btn-success"
            href="javascript:;"
            onclick="createSchedule('<?php echo e($application->id); ?>')"
          >
            <?php echo app('translator')->get('modules.interviewSchedule.scheduleInterview'); ?>
          </a>
        </div>
      <?php endif; ?>
      <?php if($user->cans('add_empleopolis') && $application->status->status == 'empleopolis' && !$application->empleopolis): ?>
        <div class="text-muted resume-button">
          <a
            class="btn btn-sm btn-success"
            href="javascript:;"
            onclick="createEmpleopolis('<?php echo e($application->id); ?>')"
          >
            
            Añadir filtro interno
          </a>
        </div>
      <?php endif; ?>
      <?php if($user->cans('add_signus') && $application->status->status == 'habilitación mina'): ?>
        <div class="text-muted resume-button signus-button">
          <a
            class="btn btn-sm btn-success"
            href="javascript:;"
            onclick="createSignus('<?php echo e($application->id); ?>')"
          >
            
            Añadir filtro del cliente
          </a>
        </div>
      <?php endif; ?>
      <?php if($user->cans('add_medical_exams') && $application->status->status == 'examen médico'): ?>
        <div class="text-muted resume-button signus-button">
          <a
            class="btn btn-sm btn-success"
            href="javascript:;"
            onclick="createMedicalExam('<?php echo e($application->id); ?>')"
          >
            
            Añadir Examen Médico
          </a>
        </div>
      <?php endif; ?>
    </div>
    <div class="col-8 right-panel-box">
      <div class="col-sm-12">
        <strong><?php echo app('translator')->get('app.name'); ?></strong><br>
        <p class="text-muted"><?php echo e(ucwords($application->full_name)); ?></p>
      </div>

      <div class="col-sm-12">
        <strong><?php echo app('translator')->get('modules.jobApplication.appliedFor'); ?></strong><br>
        <p class="text-muted">
          <?php echo e(ucwords($application->job?->jobDescription?->name)); ?><br />
          <?php if($application->job->title): ?>
            <?php echo e(ucwords($application->job->title)); ?><br />
          <?php endif; ?>
          <?php echo e($application->departamento?->departamento); ?>, <?php echo e($application->provincia?->provincia); ?>

        </p>
      </div>

      <div class="col-sm-12">
        <strong><?php echo app('translator')->get('app.email'); ?></strong><br>
        <p class="text-muted"><?php echo e($application->email); ?></p>
      </div>

      <div class="col-sm-12">
        <strong><?php echo app('translator')->get('app.phone'); ?></strong><br>
        <p class="text-muted"><?php echo e($application->phone); ?></p>
      </div>

      <div class="col-sm-12">
        <div class="row">
          <?php if(!is_null($application->gender)): ?>
            <div class="col-sm-12 col-md-4">
              <strong><?php echo app('translator')->get('app.gender'); ?></strong><br>
              <p
                class="text-muted"
                id="gender-<?php echo e($application->id); ?>"
              ><?php echo e(ucfirst($application->gender)); ?></p>
            </div>
          <?php endif; ?>
          <?php if(!is_null($application->dob)): ?>
            <div class="col-sm-12 col-md-4">
              <strong><?php echo app('translator')->get('app.dob'); ?></strong><br>
              <p
                class="text-muted"
                id="dob-<?php echo e($application->id); ?>"
              ><?php echo e($application->dob->format('jS F, Y')); ?></p>
            </div>
          <?php endif; ?>
        </div>
      </div>

      <?php if(!is_null($application->address)): ?>
        <div class="col-sm-12 col-md-4">
          <strong><?php echo app('translator')->get('app.address'); ?></strong><br>
          <p
            class="text-muted"
            id="address-<?php echo e($application->id); ?>"
          ><?php echo e($application->address); ?></p>
        </div>
      <?php endif; ?>

      <?php if(!is_null($application->country)): ?>
        <div class="col-sm-12">
          <div class="row">
            <div class="col">
              <strong><?php echo app('translator')->get('app.country'); ?></strong><br>
              <p
                class="text-muted"
                id="country-<?php echo e($application->id); ?>"
              ><?php echo e($application->country); ?></p>
            </div>
            <div class="col">
              <strong><?php echo app('translator')->get('app.state'); ?></strong><br>
              <p
                class="text-muted"
                id="state-<?php echo e($application->id); ?>"
              ><?php echo e($application->state); ?></p>
            </div>
            <div class="col">
              <strong><?php echo app('translator')->get('app.city'); ?></strong><br>
              <p
                class="text-muted"
                id="city-<?php echo e($application->id); ?>"
              ><?php echo e($application->city); ?></p>
            </div>
          </div>
        </div>
      <?php endif; ?>

      <div class="col-sm-12">
        <strong><?php echo app('translator')->get('modules.jobApplication.appliedAt'); ?></strong><br>
        <p class="text-muted"><?php echo e($application->created_at->timezone($global->timezone)->format('d M, Y H:i')); ?></p>
      </div>
      <?php if(!is_null($application->cover_letter)): ?>
        <div class="col-sm-12">
          <strong><?php echo app('translator')->get('modules.jobs.coverLetter'); ?></strong><br>
          <p class="text-muted"><?php echo e($application->cover_letter); ?></p>
        </div>
      <?php endif; ?>
      <div class="col-sm-12">
        <h4><?php echo app('translator')->get('modules.front.additionalDetails'); ?></h4>
        <?php $__empty_1 = true; $__currentLoopData = $answers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $answer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
          <strong>
            <?php echo e($answer->question->question); ?>

          </strong><br>
          <?php if($answer->question->type == 'text'): ?>
            <p class="text-muted"><?php echo e(ucfirst($answer->answer)); ?></p>
          <?php else: ?>
            <?php if(!is_null($answer->file)): ?>
              <a
                class="btn btn-sm btn-primary mb-2"
                href="<?php echo e($answer->file_url); ?>"
                target="_blank"
              ><?php echo app('translator')->get('app.view'); ?> <?php echo app('translator')->get('app.file'); ?></a><br>
            <?php endif; ?>
          <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <?php endif; ?>
      </div>
      
    </div>
    <?php if($application->status->status == 'entrevistas'): ?>
      <div
        class="col-12"
        id="schedule-container"
      >
        <hr />
        <div class="row">
          <h5 class="col-sm-12"><?php echo app('translator')->get('modules.interviewSchedule.scheduleDetail'); ?></h5>
        </div>
        <table
          class="table-bordered table-striped table"
          id="interviewTable"
        >
          <thead>
            <tr>
              <th><?php echo app('translator')->get('modules.interviewSchedule.interviewTitle'); ?></th>
              <th><?php echo app('translator')->get('modules.interviewSchedule.scheduleDate'); ?></th>
              <th><?php echo app('translator')->get('modules.interviewSchedule.status'); ?></th>
              <th><?php echo app('translator')->get('app.action'); ?></th>
            </tr>
          </thead>
        </table>
      </div>
    <?php endif; ?>
    <?php if($application->status->status == 'examen médico'): ?>
      <div
        class="col-12"
        id="empleopolis-container"
      >
        <hr />
        <div class="row">
          <h5 class="col-sm-12">Examen médico</h5>
        </div>
        <table
          class="table-bordered table-striped table"
          id="medicalExamTable"
        >
          <thead>
            <tr>
              <th><?php echo app('translator')->get('menu.filter_date'); ?></th>
              <th>Cínica</th>
              <th><?php echo app('translator')->get('modules.interviewSchedule.comments'); ?></th>
              <th><?php echo app('translator')->get('menu.status'); ?></th>
              <th><?php echo app('translator')->get('app.action'); ?></th>
            </tr>
          </thead>
        </table>
      </div>
    <?php endif; ?>    
    <?php if($application->status->status == 'empleopolis'): ?>
      <div
        class="col-12"
        id="empleopolis-container"
      >
        <hr />
        <div class="row">
          <h5 class="col-sm-12">Filtro del cliente</h5>
        </div>
        <table
          class="table-bordered table-striped table"
          id="empleopolisTable"
        >
          <thead>
            <tr>
              <th><?php echo app('translator')->get('menu.filter_date'); ?></th>
              <th><?php echo app('translator')->get('menu.status'); ?></th>
              <th><?php echo app('translator')->get('app.action'); ?></th>
            </tr>
          </thead>
        </table>
      </div>
    <?php endif; ?>
    <?php if($application->status->status == 'habilitación mina'): ?>
      <div
        class="col-12"
        id="signus-container"
      >
        <hr />
        <div class="row">
          <h5 class="col-sm-12">Filtro del cliente</h5>
        </div>
        <table
          class="table-bordered table-striped table"
          id="signusTable"
        >
          <thead>
            <tr>
              <th>Nombre de filtro</th>
              <th>Fecha de Inscripción</th>
              <th>Fecha de Resultados</th>
              <th><?php echo app('translator')->get('menu.status'); ?></th>
              <th><?php echo app('translator')->get('app.action'); ?></th>
            </tr>
          </thead>
        </table>
      </div>
    <?php endif; ?>
    <?php if($application->status->status == 'oferta de trabajo' && !is_null($application->onboard)): ?>
      <div
        class="col-12"
        id="onboard-container"
      >
        <hr />
        <div class="row">
          <h5 class="col-sm-12">Oferta de trabajo</h5>
        </div>
        <table
          class="table-bordered table-striped table"
          id="jobOnboardTable"
        >
          <thead>
            <tr>
              <th><?php echo app('translator')->get('menu.jobs'); ?></th>
              <th><?php echo app('translator')->get('app.joinDate'); ?></th>
              <th><?php echo app('translator')->get('app.acceptLastDate'); ?></th>
              <th><?php echo app('translator')->get('app.status'); ?></th>
              <th><?php echo app('translator')->get('app.action'); ?></th>
            </tr>
          </thead>
        </table>
      </div>
    <?php endif; ?>
    <?php if($application->status->status == 'validación documentaria'): ?>
      <div class="col-12" id="validacion-documentaria-container">
        <hr />
        <div class="row">
          <h5 class="col-sm-12">Documentación</h5>
        </div>
        <table
          class="table-bordered table-striped table no-footer"  
          id="documentationTable"
        >
          <thead>
            <tr>
              <th>Documento</th>
              <th>¿Requerido?</th>
              <th>Acciones</th>
            </tr>
          </thead>
          <tbody>
            <?php $__currentLoopData = $documentation; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <?php
                $required_document = $application->required_documents->where('documentation_id', $item->id)->first();
              ?>
              <tr>
                <td><?php echo e($item->name); ?></td>
                <td>
                  <div class="form-check">
                    <input 
                      type="checkbox"
                      data-size="small"
                      class="js-switch documentation-checkbox" 
                      <?php if($required_document?->is_required): ?> checked <?php endif; ?>
                      data-id="<?php echo e($item->id); ?>"
                    >
                    <label></label>
                  </div>                  
                </td>
                <td id="actions-<?php echo e($item->id); ?>" class="text-nowrap">
                  <?php if($required_document?->is_required): ?>
                    <?php if($required_document->document): ?>
                      <a
                        class="btn btn-primary btn-circle"
                        data-toggle="tooltip"
                        href="/user-uploads/documents/<?php echo e($application->id); ?>/<?php echo e($required_document->document->hashname); ?>"
                        target="_blank"
                        title="<?php echo app('translator')->get('app.download'); ?>"
                      >
                        <i class="fa fa-download" aria-hidden="true"></i>
                      </a>
                      <a
                        href="#"
                        class="btn btn-danger btn-circle sa-params delete-required-document"
                        data-toggle="tooltip"
                        onclick="deleteRequiredDocument(<?php echo e($application->id); ?>, <?php echo e($item->id); ?>)" 
                        title="<?php echo app('translator')->get('app.delete'); ?>"
                      >
                        <i class="fa fa-times" aria-hidden="true"></i>
                      </a>
                    <?php else: ?>
                    <a
                      class="btn btn-primary btn-circle"
                      data-toggle="tooltip"
                      onclick="showUploadRequiredDocumentForm(<?php echo e($application->id); ?>, <?php echo e($item->id); ?>)"
                      target="_blank"
                    >
                      <i class="fa fa-upload text-white" aria-hidden="true"></i>
                    </a>
                    <?php endif; ?>
                  <?php endif; ?>
                </td>
              </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </table>        
      </div>
    <?php endif; ?>
    <?php if($user->cans('edit_job_applications')): ?>
      <div
        class="col-12"
        id="skills-container"
      >
        <hr />
        <h5><?php echo app('translator')->get('modules.jobApplication.skills'); ?></h5>
        <div class="form-group mb-2">
          <select
            class="form-control select2 custom-select"
            id="skills"
            multiple
            name="skills[]"
          >
            <?php $__empty_1 = true; $__currentLoopData = $skills; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $skill): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
              <option
                <?php if(!is_null($application->skills) && in_array($skill->id, $application->skills)): ?> selected <?php endif; ?>
                value="<?php echo e($skill->id); ?>"
              ><?php echo e($skill->name); ?></option>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <?php endif; ?>
          </select>
        </div>
        <a
          class="btn btn-sm btn-outline-success"
          href="javascript:addSkills(<?php echo e($application->id); ?>);"
          id="add-skills"
        >
          <?php if(!is_null($application->skills) && sizeof($application->skills) > 0): ?>
            <?php echo app('translator')->get('modules.jobApplication.updateSkills'); ?>
          <?php else: ?>
            <?php echo app('translator')->get('modules.jobApplication.addSkills'); ?>
          <?php endif; ?>
        </a>
      </div>
    <?php endif; ?>
    <div class="col-12">
      <hr>
      <div class="row">
        <div class="col-sm-12 mb-3">
          <h5><?php echo app('translator')->get('modules.jobApplication.applicantNotes'); ?></h5>
        </div>
        <div
          class="col-sm-12"
          id="applicant-notes"
        >
          <ul class="list-unstyled">
            <?php $__currentLoopData = $application->notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $notes): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <li
                class="media mb-3"
                id="note-<?php echo e($notes->id); ?>"
              >
                <div class="media-body">
                  <h6 class="mb-1 mt-0"><?php echo e(ucwords($notes->user->name)); ?>

                    <span class="pull-right font-italic font-weight-light"><small>
                        <?php echo e($notes->created_at->diffForHumans()); ?> </small>
                      <?php if($user->cans('edit_job_applications')): ?>
                        <a
                          class="edit-note"
                          data-note-id="<?php echo e($notes->id); ?>"
                          href="javascript:;"
                        ><i
                            class="fa fa-edit ml-2"
                            title="Edit"
                          ></i></a>
                        <a
                          class="delete-note"
                          data-note-id="<?php echo e($notes->id); ?>"
                          href="javascript:;"
                          title="Delete"
                        ><i class="fa fa-trash text-danger ml-1"></i></a>
                      <?php endif; ?>
                    </span>
                  </h6>
                  <small class="note-text"><?php echo e(ucfirst($notes->note_text)); ?></small>
                  <div class="note-textarea"></div>
                </div>
              </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </ul>
        </div>
      </div>

      <?php if($user->cans('edit_job_applications')): ?>
        <div class="row">
          <div class="col-sm-12">
            <div class="form-group mb-2">
              <textarea
                class="form-control"
                cols="30"
                id="note_text"
                name="note"
                rows="4"
              ></textarea>
            </div>
            <a
              class="btn btn-sm btn-outline-primary"
              href="javascript:;"
              id="add-note"
            ><?php echo app('translator')->get('modules.jobApplication.addNote'); ?></a>
          </div>
        </div>
      <?php endif; ?>
    </div>
  </div>
</div>
<script>
  $('.js-switch').each(function() {
    new Switchery($(this)[0], $(this).data());
  });
  $('.documentation-checkbox').on('change', function() {
    let documentation_id = $(this).data('id');
    let url = "<?php echo e(route('admin.job-applications.updateDocumentation', $application->id)); ?>";
    let token = '<?php echo e(csrf_token()); ?>';
    let checked = $(this).is(':checked') ? 1 : 0;
    $.easyAjax({
      type: 'POST',
      url: url,
      data: {
        '_token': token,
        'is_required': checked,
        'documentation_id': documentation_id
      },
      success: function(response) {
        if (response.requiredDocument.is_required) {
          if (response.requiredDocument.document) {
            $('#actions-' + documentation_id).html(
              `<a
                class="btn btn-primary btn-circle"
                data-toggle="tooltip"
                href="/user-uploads/documents/<?php echo e($application->id); ?>/${response.requiredDocument.document.hashname}"
                title="<?php echo app('translator')->get('app.download'); ?>"
                target="_blank"
              >
                <i class="fa fa-download" aria-hidden="true"></i>
              </a>
              <a href="#" 
                class="btn btn-danger btn-circle sa-params delete-required-document"
                data-toggle="tooltip"
                onclick="deleteRequiredDocument(<?php echo e($application->id); ?>, ${response.requiredDocument.documentation.id})" 
                title="<?php echo app('translator')->get('app.delete'); ?>"
              >
                <i class="fa fa-times" aria-hidden="true"></i>
              </a>`
            );
          } else {
            $('#actions-' + documentation_id).html(
              `<a
                class="btn btn-primary btn-circle"
                data-toggle="tooltip"
                onclick="showUploadRequiredDocumentForm(${response.requiredDocument.job_application_id}, ${response.requiredDocument.documentation_id})"
                title="<?php echo app('translator')->get('app.upload'); ?>"
              >
                <i class="fa fa-upload text-white" aria-hidden="true"></i>
              </a>`
            );
          }
        } else {
          $('#actions-' + documentation_id).html('');
        }
      }
    });
  });
  <?php if($application->status->status == 'entrevistas'): ?>
    var table = $('#interviewTable').dataTable({
      responsive: true,
      processing: true,
      serverSide: true,
      destroy: true,
      searching: false,
      ordering: false,
      paging: false,
      ajax: {
        'url': '<?php echo route('admin.interview-schedule.data-by-candidate', $application->id); ?>',
      },
      language: languageOptions(),
      "fnDrawCallback": function(oSettings) {
        $("body").tooltip({
          selector: '[data-toggle="tooltip"]'
        });
      },
      columns: [{
          data: 'title',
          name: 'title',
          orderable: false,
          searchable: false
        },
        {
          data: 'schedule_date',
          name: 'schedule_date',
          orderable: false,
          searchable: false
        },
        {
          data: 'status',
          name: 'status',
          orderable: false,
          searchable: false
        },
        {
          data: 'action',
          name: 'action',
          className: 'text-nowrap',
          width: '20px',
          orderable: false,
          searchable: false
        }
      ],
      order: [
        [1, 'asc']
      ]
    });
  <?php endif; ?>
  <?php if($application->status->status == 'empleopolis'): ?>
    var table = $('#empleopolisTable').dataTable({
      responsive: true,
      processing: true,
      serverSide: true,
      destroy: true,
      searching: false,
      ordering: false,
      paging: false,
      ajax: {
        'url': '<?php echo route('admin.empleopolis.data-by-candidate', $application->id); ?>',
      },
      language: languageOptions(),
      "fnDrawCallback": function(oSettings) {
        $("body").tooltip({
          selector: '[data-toggle="tooltip"]'
        });
      },
      columns: [{
          data: 'filter_date',
          name: 'filter_date',
          orderable: false,
          searchable: false
        },
        {
          data: 'status',
          name: 'status',
          orderable: false,
          searchable: false
        },
        {
          data: 'action',
          name: 'action',
          className: 'text-nowrap',
          width: '20px',
          orderable: false,
          searchable: false
        }
      ],
      order: [
        [1, 'asc']
      ]
    });
  <?php endif; ?>
  <?php if($application->status->status == 'habilitación mina'): ?>
    var signus_table = $('#signusTable').dataTable({
      responsive: true,
      processing: true,
      serverSide: true,
      destroy: true,
      searching: false,
      ordering: false,
      paging: false,
      ajax: {
        'url': '<?php echo route('admin.signus.data-by-candidate', $application->id); ?>',
      },
      language: languageOptions(),
      "fnDrawCallback": function(oSettings) {
        $("body").tooltip({
          selector: '[data-toggle="tooltip"]'
        });
      },
      columns: [
        {
          data: 'name',
          name: 'name',
          orderable: false,
          searchable: false
        },
        {
          data: 'inscription_date',
          name: 'inscription_date',
          orderable: false,
          searchable: false
        },
        {
          data: 'signus_date',
          name: 'signus_date',
          orderable: false,
          searchable: false
        },
        {
          data: 'status',
          name: 'status',
          orderable: false,
          searchable: false
        },
        {
          data: 'action',
          name: 'action',
          className: 'text-nowrap',
          width: '20px',
          orderable: false,
          searchable: false
        }
      ],
      order: [
        [1, 'asc']
      ]
    });
  <?php endif; ?>
  <?php if($application->status->status == 'examen médico'): ?>
    var table = $('#medicalExamTable').dataTable({
      responsive: true,
      processing: true,
      serverSide: true,
      destroy: true,
      searching: false,
      ordering: false,
      paging: false,
      ajax: {
        'url': '<?php echo route('admin.medical-exam.data-by-candidate', $application->id); ?>',
      },
      language: languageOptions(),
      "fnDrawCallback": function(oSettings) {
        $("body").tooltip({
          selector: '[data-toggle="tooltip"]'
        });
      },
      columns: [{
          data: 'medical_exam_date',
          name: 'medical_exam_date',
          orderable: false,
          searchable: false
        },
        {
          data: 'clinic',
          name: 'clinic',
          orderable: false,
          searchable: false
        },
        {
          data: 'comments',
          name: 'comments',
          orderable: false,
          searchable: false
        },
        {
          data: 'status',
          name: 'status',
          orderable: false,
          searchable: false
        },
        {
          data: 'action',
          name: 'action',
          className: 'text-nowrap',
          width: '20px',
          orderable: false,
          searchable: false
        }
      ],
      order: [
        [1, 'asc']
      ]
    });
  <?php endif; ?>  
  <?php if($application->status->status == 'oferta de trabajo'): ?>
    table = $('#jobOnboardTable').dataTable({
      responsive: true,
      processing: true,
      serverSide: true,
      destroy: true,
      searching: false,
      ordering: false,
      paging: false,
      ajax: '<?php echo route('admin.job-onboard.data-by-candidate', $application->id); ?>',
      language: languageOptions(),
      "fnDrawCallback": function(oSettings) {
        $("body").tooltip({
          selector: '[data-toggle="tooltip"]'
        });
      },
      columns: [{
          data: 'title',
          name: 'jobs.title',
        },
        {
          data: 'joining_date',
          name: 'joining_date'
        },
        {
          data: 'accept_last_date',
          name: 'accept_last_date'
        },
        {
          data: 'hired_status',
          name: 'hired_status'
        },
        {
          data: 'action',
          name: 'action',
          className: 'text-nowrap',
          width: '20px',
          orderable: false,
          searchable: false
        }
      ]
    });
  <?php endif; ?>
</script>
<?php if($user->cans('edit_job_applications')): ?>
  <script
    src="<?php echo e(asset('assets/plugins/jquery-bar-rating-master/dist/jquery.barrating.min.js')); ?>"
    type="text/javascript"
  ></script>
  <script>
    $('#example-fontawesome').barrating({
      theme: 'fontawesome-stars',
      showSelectedRating: false,
      onSelect: function(value, text, event) {
        if (event !== undefined && value !== '') {
          var url = "<?php echo e(route('admin.job-applications.rating-save', ':id')); ?>";
          url = url.replace(':id', <?php echo e($application->id); ?>);
          var token = '<?php echo e(csrf_token()); ?>';
          var id = <?php echo e($application->id); ?>;
          $.easyAjax({
            type: 'Post',
            url: url,
            container: '#example-fontawesome',
            data: {
              'rating': value,
              '_token': token
            },
            success: function(response) {
              $('#example-fontawesome_' + id).barrating('set', value);
            }
          });
        }

      }
    });
    <?php if($application->rating !== null): ?>
      $('#example-fontawesome').barrating('set', <?php echo e($application->rating); ?>);
    <?php endif; ?>
  </script>
<?php endif; ?>
<script>
  $('.select2#skills').select2();

  function addSkills(applicationId) {
    let url = "<?php echo e(route('admin.job-applications.addSkills', ':id')); ?>";
    url = url.replace(':id', applicationId);

    $.easyAjax({
      url: url,
      type: 'POST',
      container: '#skills-container',
      data: {
        _token: '<?php echo e(csrf_token()); ?>',
        skills: $('#skills').val()
      },
      success: function(response) {
        if (response.status === 'success') {
          $("body").removeClass("control-sidebar-slide-open");
          if (typeof table !== 'undefined') {
            table._fnDraw();
          } else {
            loadData();
          }
        }
      }
    })

  }

  function rejectApplication(applicationId) {
    swal({
      title: "<?php echo app('translator')->get('errors.areYouSure'); ?>",
      text: "<?php echo app('translator')->get('errors.rejectWarning'); ?>",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#DD6B55",
      confirmButtonText: "<?php echo app('translator')->get('app.reject'); ?>",
      cancelButtonText: "<?php echo app('translator')->get('app.cancel'); ?>",
      closeOnConfirm: true,
      closeOnCancel: true
    }, function(isConfirm) {
      if (isConfirm) {

        var url = "<?php echo e(route('admin.job-applications.rejectJobApplication', ':id')); ?>";
        url = url.replace(':id', applicationId);

        var token = '<?php echo e(csrf_token()); ?>';

        $.easyAjax({
          type: 'POST',
          url: url,
          data: {
            '_token': token
          },
          success: function(response) {
            $("body").removeClass("control-sidebar-slide-open");
            if (response.status === 'success') {
              if (typeof table !== 'undefined') {
                table._fnDraw();
              } else {
                loadData();
              }
            }
          }
        });
      }
    });
  }

  function archiveApplication(applicationId) {
    swal({
      title: "<?php echo app('translator')->get('errors.areYouSure'); ?>",
      text: "<?php echo app('translator')->get('errors.archiveWarning'); ?>",
      type: "info",
      showCancelButton: true,
      confirmButtonColor: "#28A745",
      confirmButtonText: "<?php echo app('translator')->get('app.archive'); ?>",
      cancelButtonText: "<?php echo app('translator')->get('app.cancel'); ?>",
      closeOnConfirm: true,
      closeOnCancel: true
    }, function(isConfirm) {
      if (isConfirm) {

        var url = "<?php echo e(route('admin.job-applications.archiveJobApplication', ':id')); ?>";
        url = url.replace(':id', applicationId);

        var token = '<?php echo e(csrf_token()); ?>';

        $.easyAjax({
          type: 'POST',
          url: url,
          data: {
            '_token': token
          },
          success: function(response) {
            $("body").removeClass("control-sidebar-slide-open");
            if (response.status === 'success') {
              if (typeof table != 'undefined') {
                table._fnDraw();
              }
              if (typeof loadData != 'undefined') {
                loadData();
              }
            }
          }
        });
      }
    });
  }

  $('#add-note').click(function() {
    var url = "<?php echo e(route('admin.applicant-note.store')); ?>";
    var id = <?php echo e($application->id); ?>;
    var note = $('#note_text').val();
    var token = '<?php echo e(csrf_token()); ?>';

    $.easyAjax({
      type: 'POST',
      url: url,
      data: {
        '_token': token,
        'id': id,
        'note': note
      },
      success: function(response) {
        if (response.status == 'success') {
          $('#applicant-notes').html(response.view);
          $('#note_text').val('');
        }
      }
    });
  });

  $('body').on('click', '.edit-note', function() {
    $(this).hide();
    let noteId = $(this).data('note-id');
    $('body').find('#note-' + noteId + ' .note-text').hide();

    let noteText = $('body').find('#note-' + noteId + ' .note-text').html();
    let textArea = '<textarea id="edit-note-text-' + noteId + '" class="form-control" row="4">' + noteText +
      '</textarea><a class="update-note" data-note-id="' + noteId +
      '" href="javascript:;"><i class="fa fa-check"></i> <?php echo app('translator')->get('modules.jobApplication.appliedFor'); ?></a>';
    $('body').find('#note-' + noteId + ' .note-textarea').html(textArea);
  });

  $('body').on('click', '.update-note', function() {
    let noteId = $(this).data('note-id');

    var url = "<?php echo e(route('admin.applicant-note.update', ':id')); ?>";
    url = url.replace(':id', noteId);

    var note = $('#edit-note-text-' + noteId).val();
    var token = '<?php echo e(csrf_token()); ?>';

    $.easyAjax({
      type: 'POST',
      url: url,
      data: {
        '_token': token,
        'noteId': noteId,
        'note': note,
        '_method': 'PUT'
      },
      success: function(response) {
        if (response.status == 'success') {
          $('#applicant-notes').html(response.view);
        }
      }
    });
  });

  $('body').on('click', '.delete-note', function() {
    let noteId = $(this).data('note-id');
    swal({
      title: "<?php echo app('translator')->get('errors.areYouSure'); ?>",
      text: "<?php echo app('translator')->get('errors.deleteWarning'); ?>",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#DD6B55",
      confirmButtonText: "<?php echo app('translator')->get('app.delete'); ?>",
      cancelButtonText: "<?php echo app('translator')->get('app.cancel'); ?>",
      closeOnConfirm: true,
      closeOnCancel: true
    }, function(isConfirm) {
      if (isConfirm) {

        var url = "<?php echo e(route('admin.applicant-note.destroy', ':id')); ?>";
        url = url.replace(':id', noteId);

        var token = '<?php echo e(csrf_token()); ?>';

        $.easyAjax({
          type: 'POST',
          url: url,
          data: {
            '_token': token,
            '_method': 'DELETE'
          },
          success: function(response) {
            if (response.status == 'success') {
              $('#applicant-notes').html(response.view);
            }
          }
        });
      }
    });
  });

  // Schedule create modal view
  function createSchedule(id) {
    var url = "<?php echo e(route('admin.job-applications.create-schedule', ':id')); ?>";
    url = url.replace(':id', id);
    $('#modelHeading').html('Schedule');
    $('#scheduleDetailModal').modal('hide');
    $.ajaxModal('#scheduleDetailModal', url);
  }

  // Schedule Edit Data Modal
  <?php if($user->cans('edit_schedule')): ?>
    function editSchedule(id) {
      var url = "<?php echo e(route('admin.interview-schedule.edit', ':id')); ?>";
      url = url.replace(':id', id);
      $('#modelHeading').html('Schedule');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Schedule View Data Modal
  <?php if($user->cans('view_schedule')): ?>
    function showSchedule(id) {
      var url = "<?php echo e(route('admin.interview-schedule.show', ':id')); ?>?table=yes";
      url = url.replace(':id', id);
      $('#modelHeading').html('Schedule');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Empleopolis create modal view
  <?php if($user->cans('add_empleopolis')): ?>
    function createEmpleopolis(id) {
      var url = "<?php echo route('admin.empleopolis.create', ':id'); ?>";
      url = url.replace(':id', 'id=' + id);
      $('#modelHeading').html('Empleopolis');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Empleopolis edit modal 
  <?php if($user->cans('edit_empleopolis')): ?>
    function editEmpleopolis(id) {
      var url = "<?php echo e(route('admin.empleopolis.edit', ':id')); ?>";
      url = url.replace(':id', id);
      $('#modelHeading').html('Empleopolis');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Empleopolis modal view
  <?php if($user->cans('view_empleopolis')): ?>
    function showEmpleopolis(id) {
      var url = "<?php echo e(route('admin.empleopolis.show', ':id')); ?>";
      url = url.replace(':id', id);
      $('#modelHeading').html('Empleopolis');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Signus create modal view
  <?php if($user->cans('add_signus')): ?>
    function createSignus(id) {
      var url = "<?php echo e(route('admin.signus.create', ':id')); ?>";
      url = url.replace(':id', 'id=' + id);
      $('#modelHeading').html('Filtro del cliente');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Signus Edit Data Modal
  <?php if($user->cans('edit_signus')): ?>
    function editSignus(id) {
      var url = "<?php echo e(route('admin.signus.edit', ':id')); ?>";
      url = url.replace(':id', id);
      $('#modelHeading').html('Filtro del cliente');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Signus View Data Modal
  <?php if($user->cans('view_signus')): ?>
    function showSignus(id) {
      var url = "<?php echo e(route('admin.signus.show', ':id')); ?>";
      url = url.replace(':id', id);
      $('#modelHeading').html('Filtro del cliente');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>
  
  // Medical Exam create modal view
  <?php if($user->cans('add_medical_exams')): ?>
    function createMedicalExam(id) {
      var url = "<?php echo e(route('admin.medical-exam.create', ':id')); ?>";
      url = url.replace(':id', 'id=' + id);
      $('#modelHeading').html('Examen médico');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // MedicalExam Edit Data Modal
  <?php if($user->cans('edit_medical_exams')): ?>
    function editMedicalExam(id) {
      var url = "<?php echo e(route('admin.medical-exam.edit', ':id')); ?>";
      url = url.replace(':id', id);
      $('#modelHeading').html('Examen médico');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // MedicalExam View Data Modal
  <?php if($user->cans('view_medical_exams')): ?>
    function showMedicalExam(id) {
      var url = "<?php echo e(route('admin.medical-exam.show', ':id')); ?>";
      url = url.replace(':id', id);
      $('#modelHeading').html('Examen médico');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }
  <?php endif; ?>

  // Job Applications upload document 
  <?php if($user->cans('edit_job_applications')): ?>
    function showUploadRequiredDocumentForm(applicationId, documentationId) {
      var url = "<?php echo e(route('admin.job-applications.showUploadRequiredDocumentForm', [':applicationId', ':documentationId'])); ?>";
      url = url.replace(':applicationId', applicationId);
      url = url.replace(':documentationId', documentationId);
      $('#modelHeading').html('Upload Document');
      $('#scheduleDetailModal').modal('hide');
      $.ajaxModal('#scheduleDetailModal', url);
    }

    function deleteRequiredDocument(applicationId, documentationId) {
      swal({
        title: "<?php echo app('translator')->get('errors.areYouSure'); ?>",
        text: "<?php echo app('translator')->get('errors.deleteWarning'); ?>",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#DD6B55",
        confirmButtonText: "<?php echo app('translator')->get('app.delete'); ?>",
        cancelButtonText: "<?php echo app('translator')->get('app.cancel'); ?>",
        closeOnConfirm: true,
        closeOnCancel: true
      }, function(isConfirm) {
        if (isConfirm) {
          var url = "<?php echo e(route('admin.job-applications.deleteRequiredDocument',  [':applicationId', ':documentationId'])); ?>";
          url = url.replace(':applicationId', applicationId);
          url = url.replace(':documentationId', documentationId);
          var token = '<?php echo e(csrf_token()); ?>';
          $.easyAjax({
            type: 'POST',
            url: url,
            data: {
              '_token': token,
              '_method': 'DELETE'
            },
            success: function(response) {
              console.log(response);
              console.log(documentationId)
              if (response.status === 'success') {
                $('#actions-' + documentationId).html(`
                  <a
                    class="btn btn-primary btn-circle"
                    data-toggle="tooltip"
                    onclick="showUploadRequiredDocumentForm(${applicationId}, ${documentationId})"
                    title="<?php echo app('translator')->get('app.upload'); ?>"
                  >
                    <i class="fa fa-upload text-white" aria-hidden="true"></i>
                  </a>
                `);
              }
            }
          });
        }
      });
    }
  <?php endif; ?>
</script>
<?php if(!is_null($application->skype_id)): ?>
  <script src="https://swc.cdn.skype.com/sdk/v1/sdk.min.js"></script>
<?php endif; ?>
<?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/job-applications/show.blade.php ENDPATH**/ ?>