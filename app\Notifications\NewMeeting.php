<?php

namespace App\Notifications;

use App\ZoomMeeting;
use App\Traits\SmtpSettings;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class NewMeeting extends Notification
{
    use Queueable, SmtpSettings;

    protected $meeting;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(ZoomMeeting $meeting)
    {
        $this->meeting = $meeting;
        $this->setMailConfigs();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $date = $this->meeting->start_date_time ? \Carbon\Carbon::parse($this->meeting->start_date_time)->format('d/m/Y') : 'Por confirmar';
        $time = $this->meeting->start_date_time ? \Carbon\Carbon::parse($this->meeting->start_date_time)->format('H:i') : 'Por confirmar';
        $location = $this->meeting->meeting_url ?? 'Virtual';
        
        return (new MailMessage)
            ->subject(__('email.newMeeting.subject'))
            ->greeting(__('email.hello').' ' . ucwords($notifiable->name) . '!')
            ->line(str_replace(['{meeting_title}', '{date}', '{time}', '{location}'], 
                [$this->meeting->meeting_name, $date, $time, $location], 
                __('email.newMeeting.body')))
            ->line(__('email.thankyouNote'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'data' => $this->meeting->toArray()
        ];
    }
}
