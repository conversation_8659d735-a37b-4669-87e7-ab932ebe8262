<?php

namespace App\Notifications;

use App\JobApplication;
use App\InterviewSchedule;
use App\Traits\SmtpSettings;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class CandidateStatusChange extends Notification
{
    use Queueable;
    use SmtpSettings;

    protected $jobApplication;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($jobApplication)
    {
        $this->jobApplication = $jobApplication;
        $this->setMailConfigs();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $companyName = config('app.name', 'SEPROCAL');

        return (new MailMessage())
            ->subject(__('email.candidateStatusUpdate.subject'))
            ->greeting(__('email.hello').' ' . ucwords($notifiable->full_name) . '!')
            ->line(str_replace(
                ['{candidate_name}', '{job_title}', '{status}', '{company_name}'],
                [$notifiable->full_name, $this->jobApplication->job->title, ucfirst($this->jobApplication->status->status), $companyName],
                __('email.candidateStatusUpdate.body')
            ))
            ->line(__('email.thankyouNote'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        //
    }

}
