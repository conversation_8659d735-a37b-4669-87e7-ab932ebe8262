<link
  href="<?php echo e(asset('assets/node_modules/bootstrap-material-datetimepicker/css/bootstrap-material-datetimepicker.css')); ?>"
  rel="stylesheet"
>
<link
  href="<?php echo e(asset('assets/node_modules/html5-editor/bootstrap-wysihtml5.css')); ?>"
  rel="stylesheet"
>
<link
  href="<?php echo e(asset('assets/node_modules/multiselect/css/multi-select.css')); ?>"
  rel="stylesheet"
>
<link
  href="<?php echo e(asset('assets/plugins/iCheck/all.css')); ?>"
  rel="stylesheet"
>
<style>
  .online-radio-button {
    display: inline-flex;
  }
</style>
<div class="modal-header">
  <h4 class="modal-title"><i class="icon-plus"></i> <?php echo app('translator')->get('modules.interviewSchedule.interviewSchedule'); ?></h4>
  <button
    aria-hidden="true"
    class="close"
    data-dismiss="modal"
    type="button"
  ><i class="fa fa-times"></i></button>
</div>
<div class="modal-body">
  <form
    class="ajax-form"
    id="createSchedule"
    method="post"
  >
    <input
      name="_token"
      type="hidden"
      value="<?php echo e(csrf_token()); ?>"
    >
    <div class="form-body">
      <div class="row">
        <div class="col-12">
          <div class="form-group">
            <label class="required"><?php echo app('translator')->get('modules.interviewSchedule.interviewTitle'); ?></label>
            <input
              class="form-control"
              id="title"
              name="title"
              type="text"
            >
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6 col-xs-12">
          <div class="form-group">
            <label class="d-block"><?php echo app('translator')->get('modules.interviewSchedule.candidate'); ?></label>
            <p><?php echo e($currentApplicant->full_name); ?></p>
            <input
              name="candidates[]"
              type="hidden"
              value="<?php echo e($currentApplicant->id); ?>"
            >
          </div>
        </div>
        <div class="col-md-6 col-xs-12">
          <div class="form-group">
            <label class="d-block"><?php echo app('translator')->get('modules.interviewSchedule.employee'); ?></label>
            <select
              class="select2 m-b-10 form-control select2-multiple"
              data-placeholder="<?php echo app('translator')->get('modules.interviewSchedule.chooseEmployee'); ?>"
              data-placeholder="<?php echo app('translator')->get('modules.interviewSchedule.employee'); ?>"
              multiple="multiple"
              name="employees[]"
            >
              <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($emp->id); ?>"><?php echo e(ucwords($emp->name)); ?> <?php if($emp->id == $user->id): ?>
                    (<?php echo app('translator')->get('app.you'); ?>)
                  <?php endif; ?>
                </option>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </select>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-xs-6 col-md-3">
          <div class="form-group">
            <label><?php echo app('translator')->get('modules.interviewSchedule.scheduleDate'); ?></label>
            <input
              class="form-control"
              id="scheduleDate"
              name="scheduleDate"
              placeholder="<?php echo app('translator')->get('modules.interviewSchedule.scheduleDate'); ?>"
              type="text"
              value="<?php echo e($scheduleDate); ?>"
            >
          </div>
        </div>

        <div class="col-xs-5 col-md-3">
          <div class="form-group chooseCandidate bootstrap-timepicker timepicker">
            <label><?php echo app('translator')->get('modules.interviewSchedule.scheduleTime'); ?></label>
            <input
              class="form-control"
              id="scheduleTime"
              name="scheduleTime"
              placeholder="<?php echo app('translator')->get('modules.interviewSchedule.scheduleTime'); ?>"
              type="text"
            >
          </div>
        </div>
        <?php if($zoom_setting->enable_zoom == 1): ?>
          <div class="col-xs-6 col-md-3">
            <div
              class="form-group"
              id="end_date_section"
              style="display: none"
            >
              <label><?php echo app('translator')->get('modules.interviewSchedule.endDate'); ?></label>
              <input
                class="form-control"
                data-placeholder="<?php echo app('translator')->get('modules.interviewSchedule.endDate'); ?>"
                id="end_date"
                name="end_date"
                type="text"
                value="<?php echo e($scheduleDate); ?>"
              >
            </div>
          </div>

          <div class="col-xs-5 col-md-3">
            <div
              class="form-group chooseCandidate bootstrap-timepicker timepicker"
              id="end_time_section"
              style="display: none"
            >
              <label><?php echo app('translator')->get('modules.interviewSchedule.scheduleTime'); ?></label>
              <input
                class="form-control"
                data-placeholder="<?php echo app('translator')->get('modules.interviewSchedule.scheduleTime'); ?>"
                id="end_time"
                name="end_time"
                type="text"
              >
            </div>
          </div>
        <?php endif; ?>

      </div>
      <?php if($zoom_setting->enable_zoom == 1): ?>

        <div class="col-xs-5 col-md-3">
          <label><?php echo app('translator')->get('modules.interviewSchedule.interviewType'); ?></label>
          <div class="form-group online-radio-button">
            <div class="">
              <input
                id="interview_typeOnline"
                name="interview_type"
                type="radio"
                value="online"
              >
              <label
                class=""
                for="interview_typeOnline"
              > <?php echo app('translator')->get('modules.meetings.online'); ?> </label>
            </div>
            <div
              class=""
              style ="margin-left: 21px;"
            >
              <input
                checked
                id="interview_typeOffline"
                name="interview_type"
                type="radio"
                value="offline"
              >
              <label
                class=""
                for="interview_typeOffline"
              > <?php echo app('translator')->get('modules.meetings.offline'); ?> </label>
            </div>
          </div>
        </div>

        <div
          class="row"
          id="repeat-fields"
          style="display: none"
        >

          <div class="col-xs-6 col-md-10">
            <div class="form-group">
              <label class="d-block"><?php echo app('translator')->get('modules.interviewSchedule.interviewTitle'); ?></label>
              <input
                class="form-control"
                id="meeting_title"
                name="meeting_title"
                type="text"
              >
            </div>
          </div>
          <div class="col-xs-12 col-md-4">
            <div class="form-group">
              <div class="m-b-10">
                <label class="control-label"><?php echo app('translator')->get('modules.zoommeeting.hostVideoStatus'); ?></label>
              </div>
              <div class="radio radio-inline">
                <input
                  id="host_video1"
                  name="host_video"
                  type="radio"
                  value="1"
                >
                <label
                  class=""
                  for="host_video1"
                > <?php echo app('translator')->get('app.enable'); ?> </label>
              </div>
              <div class="radio radio-inline">
                <input
                  checked
                  id="host_video2"
                  name="host_video"
                  type="radio"
                  value="0"
                >
                <label
                  class=""
                  for="host_video2"
                > <?php echo app('translator')->get('app.disable'); ?> </label>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <div class="m-b-10">
                <label class="control-label"><?php echo app('translator')->get('modules.zoommeeting.participantVideoStatus'); ?></label>
              </div>
              <div class="radio radio-inline">
                <input
                  id="participant_video1"
                  name="participant_video"
                  type="radio"
                  value="1"
                >
                <label
                  class=""
                  for="participant_video1"
                > <?php echo app('translator')->get('app.enable'); ?> </label>
              </div>
              <div class="radio radio-inline">
                <input
                  checked
                  id="participant_video2"
                  name="participant_video"
                  type="radio"
                  value="0"
                >
                <label
                  class=""
                  for="participant_video2"
                > <?php echo app('translator')->get('app.disable'); ?> </label>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              <label class="control-label"><?php echo app('translator')->get('modules.interviewSchedule.host'); ?></label>
              <select
                class="select2 form-control"
                id="created_by"
                name="created_by"
              >
                <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option
                    <?php if($emp->id == $user->id): ?> selected <?php endif; ?>
                    value="<?php echo e($emp->id); ?>"
                  ><?php echo e(ucwords($emp->name)); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </select>
            </div>
          </div>
          <div class="col-md-12 form-group">
            <label class="">
              <div
                aria-checked="false"
                aria-disabled="false"
                class="icheckbox_flat-green"
                style="position: relative;"
              >
                <input
                  class="flat-red"
                  id ="send_reminder"
                  name="send_reminder"
                  style="position: absolute; opacity: 0;"
                  type="checkbox"
                  value="1"
                >
                <ins
                  class="iCheck-helper"
                  style="position: absolute; top: 0%; left: 0%; display: block; width: 100%; height: 100%; margin: 0px; padding: 0px; background: rgb(255, 255, 255); border: 0px; opacity: 0;"
                ></ins>
              </div>
              <?php echo app('translator')->get('modules.zoommeeting.reminder'); ?>
            </label>

          </div>
          <div
            class="col-md-12"
            id="reminder-fields"
            style="display: none;"
          >
            <div class ="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label><?php echo app('translator')->get('modules.zoommeeting.remindBefore'); ?></label>
                  <input
                    class="form-control"
                    min="1"
                    name="remind_time"
                    type="number"
                    value="1"
                  >
                </div>
              </div>
              <div class="col-xs-6 col-md-3">
                <div class="form-group repeat_type_dropdown">
                  <label>&nbsp;</label>
                  <select
                    class="form-control"
                    id=""
                    name="remind_type"
                  >
                    <option value="day"><?php echo app('translator')->get('modules.zoommeeting.day'); ?></option>
                    <option value="hour"><?php echo app('translator')->get('modules.zoommeeting.hour'); ?></option>
                    <option value="minute"><?php echo app('translator')->get('modules.zoommeeting.minute'); ?></option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div>
      <?php endif; ?>

      <div class="row">
        <div class="col-xs-12 col-md-12">
          <div class="form-group">
            <label><?php echo app('translator')->get('modules.interviewSchedule.comment'); ?></label>
            <textarea
              class="form-control"
              id="comment"
              name="comment"
              placeholder="<?php echo app('translator')->get('modules.interviewSchedule.comment'); ?>"
              type="text"
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </form>

</div>
<div class="modal-footer">
  <button
    class="btn btn-outline dark"
    data-dismiss="modal"
    type="button"
  ><?php echo app('translator')->get('app.close'); ?></button>
  <button
    class="btn btn-success save-schedule"
    type="button"
  ><?php echo app('translator')->get('app.submit'); ?></button>
</div>

<script
  src="<?php echo e(asset('assets/node_modules/moment/moment.js')); ?>"
  type="text/javascript"
></script>
<script src="<?php echo e(asset('assets/node_modules/multiselect/js/jquery.multi-select.js')); ?>"></script>
<script
  src="<?php echo e(asset('assets/node_modules/bootstrap-material-datetimepicker/js/bootstrap-material-datetimepicker.js')); ?>"
  type="text/javascript"
></script>
<script src="<?php echo e(asset('plugins/bootstrap-colorselector/bootstrap-colorselector.min.js')); ?>"></script>


<script>
  // Select 2 init.
  $(".select2").select2({
    formatNoMatches: function() {
      return "<?php echo e(__('messages.noRecordFound')); ?>";
    },
    width: '100%'
  });
  // Datepicker set
  $('#scheduleDate').bootstrapMaterialDatePicker({
    time: false,
    clearButton: true,
  });
  $('#end_date').bootstrapMaterialDatePicker({
    time: false,
    clearButton: true,
    minDate: new Date()
  });
  // $('#colorselector').colorselector();

  // Timepicker Set
  $('#scheduleTime').bootstrapMaterialDatePicker({
    date: false,
    shortTime: true, // look it
    format: 'HH:mm',
    switchOnClick: true
  });

  $('#end_time').bootstrapMaterialDatePicker({
    date: false,
    shortTime: true, // look it
    format: 'HH:mm',
    switchOnClick: true
  });
  $('#send_reminder').on('ifChecked', function(event) {
    $('#reminder-fields').show();
  });

  $('#send_reminder').on('ifUnchecked', function(event) {
    $('#reminder-fields').hide();
  });
  $('#send_reminder').iCheck({
    checkboxClass: 'icheckbox_flat-blue',
  })
  $('input[type=radio][name=interview_type]').change(function() {
    if (this.value == 'online') {
      $('#repeat-fields').show();
      $('#end_time_section').show();
      $('#end_date_section').show();
    } else {
      $('#repeat-fields').hide();
      $('#end_time_section').hide();
      $('#end_date_section').hide();
    }
  })
  // Save Interview Schedule
  $('.save-schedule').click(function() {
    $.easyAjax({
      url: '<?php echo e(route('admin.job-applications.store-schedule')); ?>',
      container: '#createSchedule',
      type: "POST",
      data: $('#createSchedule').serialize(),
      success: function(response) {
        if (typeof table !== 'undefined') {
          table._fnDraw();
        }
        if (typeof loadData !== 'undefined') {
          console.log('Reloading...');
          loadData();
        }
        $('#scheduleDetailModal').modal('hide');
      }
    })
  })
</script>
<?php /**PATH E:\laragon\www\recruit-seprocal\resources\views/admin/job-applications/interview-create.blade.php ENDPATH**/ ?>