<?php

namespace App\Notifications;

use App\InterviewSchedule;
use App\Traits\SmtpSettings;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class InterviewReminder extends Notification
{
    use Queueable, SmtpSettings;

    protected $interviewSchedule;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct(InterviewSchedule $interviewSchedule)
    {
        $this->interviewSchedule = $interviewSchedule;
        $this->setMailConfigs();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $date = $this->interviewSchedule->schedule_date->format('d/m/Y');
        $time = $this->interviewSchedule->schedule_date->format('H:i');
        $location = $this->interviewSchedule->comment ?? 'Por confirmar';
        
        return (new MailMessage)
            ->subject(__('email.interviewReminder.subject'))
            ->greeting(__('email.hello').' ' . ucwords($notifiable->full_name) . '!')
            ->line(str_replace(['{candidate_name}', '{job_title}', '{date}', '{time}', '{location}'], 
                [$notifiable->full_name, $this->interviewSchedule->jobApplication->job->title, $date, $time, $location], 
                __('email.interviewReminder.body')))
            ->line(__('email.thankyouNote'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'data' => $this->interviewSchedule->toArray()
        ];
    }
}
