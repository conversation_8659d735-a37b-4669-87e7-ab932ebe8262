<?php

namespace App\Notifications;

use App\SmsSetting;
use App\JobApplication;
use App\Traits\SmsSettings;
use App\Traits\SmtpSettings;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\NexmoMessage;

class JobOffer extends Notification
{
    use Queueable;
    use SmtpSettings;
    //        , SmsSettings;

    /**
     * JobOffer constructor.
     * @param JobApplication $jobApplication
     */
    public function __construct(JobApplication $jobApplication)
    {
        $this->jobApplication = $jobApplication;
        //        $this->smsSetting = SmsSetting::first();

        $this->setMailConfigs();
        //        $this->setSmsConfigs();
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        $via = ['mail'];

        //        if ($this->smsSetting->nexmo_status == 'active' && $notifiable->mobile_verified == 1) {
        //            array_push($via, 'nexmo');
        //        }

        return $via;
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $companyName = config('app.name', 'SEPROCAL');

        return (new MailMessage())
            ->subject(__('email.jobOffer.subject'))
            ->greeting(__('email.hello').' ' . ucwords($this->jobApplication->full_name) . '!')
            ->line(str_replace(
                ['{candidate_name}', '{job_title}', '{company_name}'],
                [$this->jobApplication->full_name, $this->jobApplication->job->title, $companyName],
                __('email.jobOffer.body')
            ))
            ->action(__('email.viewOffer'), route('jobs.job-offer', $this->jobApplication->onboard->offer_code))
            ->line(__('email.thankyouNote'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        //
    }

    /**
     * Get the Nexmo / SMS representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return NexmoMessage
     */
    public function toNexmo($notifiable)
    {
        return (new NexmoMessage())
                    ->content(
                        __($this->jobApplication->full_name).' '.__('email.jobOffer.text').' - ' . ucwords($this->jobApplication->job->title)
                    )->unicode();
    }
}
