<?php

namespace App\Console\Commands;

use App\InterviewSchedule;
use App\Notifications\InterviewReminder;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SendInterviewReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'interviews:send-reminders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send interview reminders to candidates';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Obtener entrevistas programadas para mañana
        $tomorrow = Carbon::tomorrow();
        
        $interviews = InterviewSchedule::with(['jobApplication'])
            ->whereDate('schedule_date', $tomorrow)
            ->where('status', 'pending')
            ->get();

        $count = 0;
        foreach ($interviews as $interview) {
            if ($interview->jobApplication) {
                $interview->jobApplication->notify(new InterviewReminder($interview));
                $count++;
            }
        }

        $this->info("Se enviaron {$count} recordatorios de entrevista.");
        
        return 0;
    }
}
